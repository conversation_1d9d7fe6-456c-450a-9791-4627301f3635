# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/ship_docking
PGHOST=localhost
PGPORT=5432
PGDATABASE=ship_docking
PGUSER=your_postgres_user
PGPASSWORD=your_postgres_password

# Session Security
SESSION_SECRET=your_very_secure_random_session_secret_here_min_32_chars

# Email Configuration (Optional - for change request notifications)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_16_character_gmail_app_password

# Application Settings
NODE_ENV=development
PORT=5000

# Production Only (set NODE_ENV=production for these to take effect)
# TRUST_PROXY=1