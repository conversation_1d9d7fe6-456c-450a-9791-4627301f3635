#!/usr/bin/env node

// Production startup script for the Ship Docking Management System
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting Ship Docking Management System...');

// Check if built files exist
const distPath = path.join(__dirname, 'client', 'dist');
if (!fs.existsSync(distPath)) {
  console.log('Built files not found. Building application...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('Build completed successfully.');
  } catch (error) {
    console.error('Build failed:', error.message);
    process.exit(1);
  }
}

// Check environment variables
const requiredEnvVars = ['DATABASE_URL', 'SESSION_SECRET'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '));
  console.error('Please check your .env file or environment configuration.');
  process.exit(1);
}

// Set NODE_ENV to production if not set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// Start the server
console.log('Starting server in production mode...');
require('./server/index.ts');