import nodemailer from 'nodemailer';
import { BookingWithDetails, User } from '@shared/schema';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email configuration with fallback to console logging
const createTransporter = () => {
  // Check if email credentials are available
  if (!process.env.GMAIL_USER || !process.env.GMAIL_APP_PASSWORD) {
    console.log('Email credentials not configured - using console fallback');
    return null;
  }

  const config: EmailConfig = {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.GMAIL_USER || '',
      pass: process.env.GMAIL_APP_PASSWORD || '',
    },
  };

  const transporter = nodemailer.createTransport({
    ...config,
    tls: {
      rejectUnauthorized: false
    },
    debug: false, // Disable debug for production
    logger: false,
  });

  return transporter;
};

export interface BookingChangeRequest {
  booking: BookingWithDetails;
  requestedChanges: any;
  requestedBy: User;
  reason?: string;
}

// Fallback notification function that logs to console
function logNotificationToConsole(adminEmail: string, changeRequest: BookingChangeRequest) {
  const { booking, requestedChanges, requestedBy, reason } = changeRequest;
  
  console.log('\n=== BOOKING CHANGE REQUEST NOTIFICATION ===');
  console.log(`Admin Email: ${adminEmail}`);
  console.log(`Requested by: ${requestedBy.companyName} (${requestedBy.username})`);
  console.log(`User Email: ${requestedBy.email}`);
  console.log(`Booking: ${booking.ship.name} at ${booking.dock.name}`);
  console.log(`Current Arrival: ${new Date(booking.arrivalDate).toLocaleString('de-DE')}`);
  console.log(`Current Departure: ${new Date(booking.departureDate).toLocaleString('de-DE')}`);
  
  if (requestedChanges.dockId && requestedChanges.dockId !== booking.dockId) {
    console.log(`Requested Dock Change: ID ${requestedChanges.dockId}`);
  }
  if (requestedChanges.arrivalDate && requestedChanges.arrivalDate !== booking.arrivalDate) {
    console.log(`Requested Arrival Change: ${new Date(requestedChanges.arrivalDate).toLocaleString('de-DE')}`);
  }
  if (requestedChanges.departureDate && requestedChanges.departureDate !== booking.departureDate) {
    console.log(`Requested Departure Change: ${new Date(requestedChanges.departureDate).toLocaleString('de-DE')}`);
  }
  if (reason) {
    console.log(`Reason: ${reason}`);
  }
  console.log('==========================================\n');
}

export async function sendBookingChangeNotification(
  adminEmail: string,
  changeRequest: BookingChangeRequest
): Promise<boolean> {
  try {
    console.log('Processing booking change notification...');
    console.log('Target admin email:', adminEmail);
    
    const transporter = createTransporter();
    
    // If no transporter available, use console fallback
    if (!transporter) {
      console.log('Email transporter not available - using console notification');
      logNotificationToConsole(adminEmail, changeRequest);
      return true; // Return true as notification was "sent" via console
    }
    
    // Try to verify and send email
    try {
      await transporter.verify();
      console.log('Email transporter verified successfully');
    } catch (verifyError) {
      console.error('Email transporter verification failed, using console fallback');
      logNotificationToConsole(adminEmail, changeRequest);
      return true; // Return true as notification was "sent" via console
    }
    
    const { booking, requestedChanges, requestedBy, reason } = changeRequest;
    
    // Format the requested changes for email
    const formatChanges = (changes: any) => {
      const changeLines: string[] = [];
      
      if (changes.dockId && changes.dockId !== booking.dockId) {
        changeLines.push(`• Neue Lände: ${changes.dockName || 'ID: ' + changes.dockId}`);
      }
      if (changes.arrivalDate && changes.arrivalDate !== booking.arrivalDate) {
        changeLines.push(`• Neue Ankunftszeit: ${new Date(changes.arrivalDate).toLocaleString('de-DE')}`);
      }
      if (changes.departureDate && changes.departureDate !== booking.departureDate) {
        changeLines.push(`• Neue Abfahrtszeit: ${new Date(changes.departureDate).toLocaleString('de-DE')}`);
      }
      if (changes.row && changes.row !== booking.row) {
        changeLines.push(`• Neue Reihe: ${changes.row}`);
      }
      if (changes.notes && changes.notes !== booking.notes) {
        changeLines.push(`• Neue Notizen: ${changes.notes}`);
      }
      
      return changeLines.join('\n');
    };

    const emailHtml = `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
              Neue Buchungsänderungsanfrage
            </h2>
            
            <div style="background-color: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #374151;">Antragsteller</h3>
              <p><strong>Firma:</strong> ${requestedBy.companyName}</p>
              <p><strong>Benutzername:</strong> ${requestedBy.username}</p>
              <p><strong>E-Mail:</strong> ${requestedBy.email}</p>
              ${requestedBy.phoneNumber ? `<p><strong>Telefon:</strong> ${requestedBy.phoneNumber}</p>` : ''}
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #92400e;">Aktuelle Buchung</h3>
              <p><strong>Schiff:</strong> ${booking.ship.name}</p>
              <p><strong>Aktuelle Lände:</strong> ${booking.dock.name} (${booking.dock.location})</p>
              <p><strong>Aktuelle Ankunft:</strong> ${new Date(booking.arrivalDate).toLocaleString('de-DE')}</p>
              <p><strong>Aktuelle Abfahrt:</strong> ${new Date(booking.departureDate).toLocaleString('de-DE')}</p>
              ${booking.row ? `<p><strong>Aktuelle Reihe:</strong> ${booking.row}</p>` : ''}
              ${booking.notes ? `<p><strong>Aktuelle Notizen:</strong> ${booking.notes}</p>` : ''}
            </div>

            <div style="background-color: #dcfce7; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #166534;">Gewünschte Änderungen</h3>
              <div style="white-space: pre-line;">${formatChanges(requestedChanges)}</div>
            </div>

            ${reason ? `
              <div style="background-color: #e0e7ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #3730a3;">Begründung</h3>
                <p>${reason}</p>
              </div>
            ` : ''}

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
              <p style="color: #6b7280; font-size: 14px;">
                Diese Anfrage wurde automatisch vom Schiffsbuchungssystem generiert.<br>
                Bitte loggen Sie sich in das Admin-Panel ein, um die Anfrage zu bearbeiten.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;

    const mailOptions = {
      from: process.env.GMAIL_USER,
      to: adminEmail,
      subject: `Buchungsänderungsanfrage von ${requestedBy.companyName} - ${booking.ship.name}`,
      html: emailHtml,
    };

    await transporter!.sendMail(mailOptions);
    console.log('Email sent successfully to:', adminEmail);
    return true;
  } catch (error) {
    console.error('Error sending email, using console fallback:', error);
    logNotificationToConsole(adminEmail, changeRequest);
    return true; // Return true as notification was handled via console
  }
}