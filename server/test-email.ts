import { sendBookingChangeNotification } from './email';
import { storage } from './storage';

async function testEmail() {
  try {
    console.log('Testing email configuration...');
    
    // Check environment variables
    console.log('GMAIL_USER:', process.env.GMAIL_USER ? 'Set' : 'Not set');
    console.log('GMAIL_APP_PASSWORD:', process.env.GMAIL_APP_PASSWORD ? 'Set' : 'Not set');
    
    // Get a test booking and user
    const booking = await storage.getBooking(1);
    const user = await storage.getUser(1);
    
    if (!booking || !user) {
      console.log('No test data available');
      return;
    }
    
    const testEmail = '<EMAIL>'; // You can change this to your email
    
    const result = await sendBookingChangeNotification(testEmail, {
      booking,
      requestedChanges: {
        dockId: 2,
        dockName: 'Test Dock',
        arrivalDate: new Date().toISOString(),
        reason: 'Test email notification'
      },
      requestedBy: user,
      reason: 'Testing email functionality'
    });
    
    console.log('Email test result:', result);
  } catch (error) {
    console.error('Email test failed:', error);
  }
}

// Only run if called directly
if (require.main === module) {
  testEmail();
}

export { testEmail };