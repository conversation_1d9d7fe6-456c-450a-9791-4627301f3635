import type { Express } from "express";
import { createServer, type Server } from "http";
import { z } from "zod";
import { setupAuth } from "./auth";
import { storage } from "./storage";
import { insertBookingSchema, insertShipSchema } from "@shared/schema";
import { sendBookingChangeNotification } from "./email";

export async function registerRoutes(app: Express): Promise<Server> {
  // Set up authentication routes (email/password)
  setupAuth(app);



  // Middleware to check if user is authenticated
  const ensureAuthenticated = (req: any, res: any, next: any) => {
    if (req.isAuthenticated()) {
      return next();
    }
    res.status(401).json({ message: "Unauthorized" });
  };

  // Middleware to check if user is admin
  const ensureAdmin = (req: any, res: any, next: any) => {
    if (req.isAuthenticated() && req.user.isAdmin) {
      return next();
    }
    res.status(403).json({ message: "Admin access required" });
  };

  // Ship routes
  app.get("/api/ships", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const ships = await storage.getShipsByUser(userId);
      res.json(ships);
    } catch (error) {
      res.status(500).json({ message: "Error retrieving ships" });
    }
  });

  app.post("/api/ships", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const shipData = insertShipSchema.parse({ ...req.body, userId });
      const ship = await storage.createShip(shipData);
      res.status(201).json(ship);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({ message: "Invalid ship data", errors: error.errors });
      } else {
        res.status(500).json({ message: "Error creating ship" });
      }
    }
  });

  app.put("/api/ships/:id", ensureAuthenticated, async (req, res) => {
    try {
      const shipId = parseInt(req.params.id);
      const userId = req.user!.id;
      
      // Verify the ship belongs to the user
      const ship = await storage.getShip(shipId);
      if (!ship) {
        return res.status(404).json({ message: "Ship not found" });
      }
      if (ship.userId !== userId) {
        return res.status(403).json({ message: "Not authorized" });
      }
      
      const shipData = req.body;
      const updatedShip = await storage.updateShip(shipId, shipData);
      res.json(updatedShip);
    } catch (error) {
      res.status(500).json({ message: "Error updating ship" });
    }
  });

  app.delete("/api/ships/:id", ensureAuthenticated, async (req, res) => {
    try {
      const shipId = parseInt(req.params.id);
      const userId = req.user!.id;
      
      // Verify the ship belongs to the user
      const ship = await storage.getShip(shipId);
      if (!ship) {
        return res.status(404).json({ message: "Ship not found" });
      }
      if (ship.userId !== userId) {
        return res.status(403).json({ message: "Not authorized" });
      }
      
      await storage.deleteShip(shipId);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Error deleting ship" });
    }
  });

  // Dock routes
  app.get("/api/docks", ensureAuthenticated, async (req, res) => {
    try {
      const docks = await storage.getAllDocks();
      res.json(docks);
    } catch (error) {
      res.status(500).json({ message: "Error retrieving docks" });
    }
  });

  app.post("/api/docks/reset", ensureAuthenticated, async (req, res) => {
    try {
      await storage.resetDocks();
      const docks = await storage.getAllDocks();
      res.json(docks);
    } catch (error) {
      res.status(500).json({ message: "Error resetting docks" });
    }
  });

  // Booking routes
  app.get("/api/bookings", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const bookings = await storage.getBookingsByUser(userId);
      res.json(bookings);
    } catch (error) {
      res.status(500).json({ message: "Error retrieving bookings" });
    }
  });

  app.get("/api/bookings/:id", ensureAuthenticated, async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const userId = req.user!.id;
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      // Verify the booking belongs to the user
      if (booking.userId !== userId) {
        return res.status(403).json({ message: "Not authorized" });
      }
      
      res.json(booking);
    } catch (error) {
      res.status(500).json({ message: "Error retrieving booking" });
    }
  });

  app.post("/api/bookings", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      
      // Manuelle Verarbeitung der Daten
      const rawBookingData = { ...req.body, userId };
      
      // Konvertiere Strings zu Date-Objekten
      if (typeof rawBookingData.arrivalDate === 'string') {
        rawBookingData.arrivalDate = new Date(rawBookingData.arrivalDate);
      }
      
      if (typeof rawBookingData.departureDate === 'string') {
        rawBookingData.departureDate = new Date(rawBookingData.departureDate);
      }
      
      // Jetzt das Schema anwenden
      const bookingData = insertBookingSchema.parse(rawBookingData);
      
      // Check if ship belongs to user
      const ship = await storage.getShip(bookingData.shipId);
      if (!ship || ship.userId !== userId) {
        return res.status(403).json({ message: "Not authorized to use this ship" });
      }
      
      const booking = await storage.createBooking(bookingData);
      res.status(201).json(booking);
    } catch (error) {
      console.error("Booking creation error:", error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ message: "Invalid booking data", errors: error.errors });
      } else {
        res.status(500).json({ message: "Error creating booking" });
      }
    }
  });

  app.put("/api/bookings/:id", ensureAuthenticated, async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const userId = req.user!.id;
      
      // Verify the booking belongs to the user
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      if (booking.userId !== userId) {
        return res.status(403).json({ message: "Not authorized" });
      }
      
      // Manuelle Verarbeitung der Daten
      const rawBookingData = { ...req.body };
      
      // Konvertiere Strings zu Date-Objekten
      if (typeof rawBookingData.arrivalDate === 'string') {
        rawBookingData.arrivalDate = new Date(rawBookingData.arrivalDate);
      }
      
      if (typeof rawBookingData.departureDate === 'string') {
        rawBookingData.departureDate = new Date(rawBookingData.departureDate);
      }
      
      const updatedBooking = await storage.updateBooking(bookingId, rawBookingData);
      res.json(updatedBooking);
    } catch (error) {
      console.error("Booking update error:", error);
      res.status(500).json({ message: "Error updating booking" });
    }
  });

  app.delete("/api/bookings/:id", ensureAuthenticated, async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const userId = req.user!.id;
      
      // Verify the booking belongs to the user
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      if (booking.userId !== userId) {
        return res.status(403).json({ message: "Not authorized" });
      }
      
      await storage.deleteBooking(bookingId);
      res.status(204).end();
    } catch (error) {
      res.status(500).json({ message: "Error deleting booking" });
    }
  });

  // Admin routes
  // Get admin settings
  app.get("/api/admin/settings", ensureAdmin, async (req, res) => {
    try {
      const settings = await storage.getAdminSettings();
      res.json(settings || { editingCutoffTime: null });
    } catch (error) {
      res.status(500).json({ message: "Error fetching admin settings" });
    }
  });

  // Update admin settings
  app.put("/api/admin/settings", ensureAdmin, async (req, res) => {
    try {
      const { editingCutoffTime, editingStartTime, editingEndTime } = req.body;
      const settings = await storage.updateAdminSettings({
        editingCutoffTime: editingCutoffTime ? new Date(editingCutoffTime) : null,
        editingStartTime: editingStartTime ? new Date(editingStartTime) : null,
        editingEndTime: editingEndTime ? new Date(editingEndTime) : null,
      });
      res.json(settings);
    } catch (error) {
      console.error("Error updating admin settings:", error);
      res.status(500).json({ message: "Error updating admin settings" });
    }
  });

  // Get all booking edit requests
  app.get("/api/admin/booking-requests", ensureAdmin, async (req, res) => {
    try {
      const requests = await storage.getBookingEditRequests();
      res.json(requests);
    } catch (error) {
      res.status(500).json({ message: "Error fetching booking edit requests" });
    }
  });

  // Approve or reject booking edit request
  app.put("/api/admin/booking-requests/:id", ensureAdmin, async (req, res) => {
    try {
      const requestId = parseInt(req.params.id);
      const { status, adminNotes } = req.body;
      const adminId = req.user!.id;

      const updatedRequest = await storage.updateBookingEditRequestStatus(
        requestId, 
        status, 
        adminNotes, 
        adminId
      );

      // If approved, apply the changes to the booking
      if (status === "approved" && updatedRequest) {
        const editRequest = await storage.getBookingEditRequest(requestId);
        if (editRequest) {
          const requestedChanges = JSON.parse(editRequest.requestedChanges);
          await storage.updateBooking(editRequest.bookingId, requestedChanges);
        }
      }

      res.json(updatedRequest);
    } catch (error) {
      res.status(500).json({ message: "Error updating booking edit request" });
    }
  });

  // Test email functionality (admin only)
  app.post("/api/admin/test-email", ensureAdmin, async (req, res) => {
    try {
      const { testEmail } = req.body;
      
      if (!testEmail) {
        return res.status(400).json({ message: "Test email address required" });
      }
      
      // Get test data
      const booking = await storage.getBooking(1);
      const user = await storage.getUser(1);
      
      if (!booking || !user) {
        return res.status(404).json({ message: "No test data available" });
      }
      
      const result = await sendBookingChangeNotification(testEmail, {
        booking,
        requestedChanges: {
          dockId: 2,
          dockName: 'Test Dock',
          arrivalDate: new Date().toISOString(),
          departureDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
          reason: 'Test email notification'
        },
        requestedBy: user,
        reason: 'Testing email functionality'
      });
      
      res.json({ 
        success: result,
        message: result ? 'Email sent successfully' : 'Email failed to send',
        gmail_user: process.env.GMAIL_USER ? 'Configured' : 'Not configured',
        gmail_password: process.env.GMAIL_APP_PASSWORD ? 'Configured' : 'Not configured'
      });
    } catch (error) {
      console.error('Email test error:', error);
      res.status(500).json({ 
        message: "Email test failed", 
        error: error instanceof Error ? error.message : 'Unknown error',
        gmail_user: process.env.GMAIL_USER ? 'Configured' : 'Not configured',
        gmail_password: process.env.GMAIL_APP_PASSWORD ? 'Configured' : 'Not configured'
      });
    }
  });

  // Create booking edit request (for users when editing is restricted)
  app.post("/api/booking-edit-requests", ensureAuthenticated, async (req, res) => {
    try {
      const userId = req.user!.id;
      const { bookingId, requestedChanges, reason } = req.body;

      const request = await storage.createBookingEditRequest({
        bookingId,
        userId,
        requestedChanges: JSON.stringify(requestedChanges),
        reason,
        status: "pending"
      });

      // Send email notification to admin
      try {
        const settings = await storage.getAdminSettings();
        if (settings?.adminEmail) {
          const booking = await storage.getBooking(bookingId);
          const user = await storage.getUser(userId);
          
          if (booking && user) {
            await sendBookingChangeNotification(settings.adminEmail, {
              booking,
              requestedChanges,
              requestedBy: user,
              reason
            });
          }
        }
      } catch (emailError) {
        console.error("Failed to send email notification:", emailError);
        // Continue even if email fails
      }

      res.status(201).json(request);
    } catch (error) {
      res.status(500).json({ message: "Error creating booking edit request" });
    }
  });

  // Check if editing is allowed for a booking
  app.get("/api/bookings/:id/can-edit", ensureAuthenticated, async (req, res) => {
    try {
      const settings = await storage.getAdminSettings();
      const now = new Date();
      
      let canEdit = true;
      let requiresRequest = false;
      let reason = "No restrictions";
      
      // Check for time-based restrictions
      if (settings?.editingCutoffTime) {
        const cutoffTime = new Date(settings.editingCutoffTime);
        if (cutoffTime <= now) {
          canEdit = false;
          requiresRequest = true;
          reason = "Past cutoff time";
        }
      }
      
      // Check for time range restrictions (start/end time)
      if (settings?.editingStartTime && settings?.editingEndTime) {
        const startTime = new Date(settings.editingStartTime);
        const endTime = new Date(settings.editingEndTime);
        
        if (now >= startTime && now <= endTime) {
          canEdit = false;
          requiresRequest = true;
          reason = "Within blocked time range";
        }
      }

      res.json({ canEdit, requiresRequest });
    } catch (error) {
      console.error("Error checking edit permissions:", error);
      res.status(500).json({ message: "Error checking edit permissions" });
    }
  });

  // Admin ship management routes
  // Get all ships for admin (all users' ships)
  app.get("/api/admin/ships", ensureAdmin, async (req, res) => {
    try {
      const allShips = await storage.getAllShipsForAdmin();
      res.json(allShips);
    } catch (error) {
      console.error("Error fetching all ships:", error);
      res.status(500).json({ message: "Error fetching ships" });
    }
  });

  // Create ship for any user (admin only)
  app.post("/api/admin/ships", ensureAdmin, async (req, res) => {
    try {
      const { userId, ...shipData } = req.body;
      
      // Validate that the user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const ship = await storage.createShip({ ...shipData, userId });
      res.status(201).json(ship);
    } catch (error) {
      console.error("Error creating ship:", error);
      res.status(500).json({ message: "Error creating ship" });
    }
  });

  // Update ship (admin only)
  app.put("/api/admin/ships/:id", ensureAdmin, async (req, res) => {
    try {
      const shipId = parseInt(req.params.id);
      const shipData = req.body;
      
      const ship = await storage.updateShip(shipId, shipData);
      if (!ship) {
        return res.status(404).json({ message: "Ship not found" });
      }
      
      res.json(ship);
    } catch (error) {
      console.error("Error updating ship:", error);
      res.status(500).json({ message: "Error updating ship" });
    }
  });

  // Delete ship (admin only)
  app.delete("/api/admin/ships/:id", ensureAdmin, async (req, res) => {
    try {
      const shipId = parseInt(req.params.id);
      
      const success = await storage.deleteShip(shipId);
      if (!success) {
        return res.status(404).json({ message: "Ship not found" });
      }
      
      res.json({ message: "Ship deleted successfully" });
    } catch (error) {
      console.error("Error deleting ship:", error);
      res.status(500).json({ message: "Error deleting ship" });
    }
  });

  // Get all users for admin (for ship creation)
  app.get("/api/admin/users", ensureAdmin, async (req, res) => {
    try {
      const users = await storage.getAllUsers();
      res.json(users);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Error fetching users" });
    }
  });

  // Update user (admin only)
  app.put("/api/admin/users/:id", ensureAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const userData = req.body;
      
      const user = await storage.updateUser(userId, userData);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      res.json(user);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Error updating user" });
    }
  });

  // Delete user (admin only)
  app.delete("/api/admin/users/:id", ensureAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      
      // Prevent admin from deleting themselves
      if (userId === req.user!.id) {
        return res.status(400).json({ message: "Cannot delete your own account" });
      }
      
      const success = await storage.deleteUser(userId);
      if (!success) {
        return res.status(404).json({ message: "User not found" });
      }
      
      res.json({ message: "User deleted successfully" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Error deleting user" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
