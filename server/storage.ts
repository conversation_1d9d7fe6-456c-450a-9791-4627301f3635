import {
  users,
  ships,
  docks,
  bookings,
  adminSettings,
  bookingEditRequests,
  type User,
  type Ship,
  type Dock,
  type Booking,
  type BookingWithDetails,
  type AdminSettings,
  type BookingEditRequest,
  type BookingEditRequestWithDetails,
  type InsertUser,
  type InsertShip,
  type InsertDock,
  type InsertBooking,
  type InsertAdminSettings,
  type InsertBookingEditRequest,
} from "@shared/schema";
import { db, pool } from "./db";
import { eq } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import createMemoryStore from "memorystore";

const MemoryStore = createMemoryStore(session);

export interface IStorage {
  // User operations (legacy)
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<User>): Promise<User | undefined>;
  
  // Replit Auth user operations
  getReplitUser(id: string): Promise<User | undefined>;
  upsertUser(user: any): Promise<User>;
  
  // Ship operations
  getShip(id: number): Promise<Ship | undefined>;
  getShipsByUser(userId: number): Promise<Ship[]>;
  createShip(ship: InsertShip): Promise<Ship>;
  updateShip(id: number, ship: Partial<Ship>): Promise<Ship | undefined>;
  deleteShip(id: number): Promise<boolean>;
  
  // Dock operations
  getDock(id: number): Promise<Dock | undefined>;
  getAllDocks(): Promise<Dock[]>;
  createDock(dock: InsertDock): Promise<Dock>;
  
  // Booking operations
  getBooking(id: number): Promise<BookingWithDetails | undefined>;
  getBookingsByUser(userId: number): Promise<BookingWithDetails[]>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBooking(id: number, booking: Partial<Booking>): Promise<Booking | undefined>;
  deleteBooking(id: number): Promise<boolean>;
  
  // Admin settings operations
  getAdminSettings(): Promise<AdminSettings | undefined>;
  updateAdminSettings(settings: Partial<AdminSettings>): Promise<AdminSettings>;
  
  // Booking edit request operations
  createBookingEditRequest(request: InsertBookingEditRequest): Promise<BookingEditRequest>;
  getBookingEditRequests(): Promise<BookingEditRequestWithDetails[]>;
  getBookingEditRequest(id: number): Promise<BookingEditRequestWithDetails | undefined>;
  updateBookingEditRequestStatus(id: number, status: string, adminNotes?: string, reviewedBy?: number): Promise<BookingEditRequest | undefined>;
  
  // Session store
  sessionStore: session.Store;
  
  // Admin ship operations
  getAllShipsForAdmin(): Promise<(Ship & { user: Omit<User, 'password'> })[]>;
  
  // Admin user operations
  getAllUsers(): Promise<Omit<User, 'password'>[]>;
  deleteUser(id: number): Promise<boolean>;
}

// Memory Storage implementation with admin user
export class MemStorage implements IStorage {
  private usersMap: Map<number, User>;
  private shipsMap: Map<number, Ship>;
  private docksMap: Map<number, Dock>;
  private bookingsMap: Map<number, Booking>;
  private userIdCounter: number;
  private shipIdCounter: number;
  private dockIdCounter: number;
  private bookingIdCounter: number;
  public sessionStore: session.Store;

  constructor() {
    this.usersMap = new Map();
    this.shipsMap = new Map();
    this.docksMap = new Map();
    this.bookingsMap = new Map();
    this.userIdCounter = 1;
    this.shipIdCounter = 1;
    this.dockIdCounter = 1;
    this.bookingIdCounter = 1;
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000, // 24 hours
    });
    
    // Initialize with docks only
    this.initializeDocks();
  }

  // Initialize predefined docks
  private initializeDocks() {
    // Clear existing docks first
    this.docksMap.clear();
    this.dockIdCounter = 1;
    
    const predefinedDocks: InsertDock[] = [
      { name: 'Engelhartszell', location: 'Engelhartszell', maxShipLength: 135, isActive: true },
      { name: 'Wesenufer', location: 'Wesenufer', maxShipLength: 120, isActive: true },
      { name: 'Schlögen', location: 'Schlögen', maxShipLength: 110, isActive: true },
      { name: 'Obermühl', location: 'Obermühl', maxShipLength: 100, isActive: true },
      { name: 'Untermühl', location: 'Untermühl', maxShipLength: 100, isActive: true },
      { name: 'Aschach', location: 'Aschach', maxShipLength: 135, isActive: true },
      { name: 'Brandstatt', location: 'Brandstatt', maxShipLength: 110, isActive: true },
      { name: 'Ottensheim', location: 'Ottensheim', maxShipLength: 135, isActive: true },
      { name: 'Linz', location: 'Linz', maxShipLength: 150, isActive: true },
      { name: 'Mauthausen', location: 'Mauthausen', maxShipLength: 135, isActive: true },
      { name: 'Enns', location: 'Enns', maxShipLength: 120, isActive: true },
      { name: 'Grein', location: 'Grein', maxShipLength: 110, isActive: true },
      { name: 'Sarmingstein', location: 'Sarmingstein', maxShipLength: 100, isActive: true }
    ];

    predefinedDocks.forEach(dock => {
      const id = this.dockIdCounter++;
      const dockWithId: Dock = { 
        ...dock, 
        id,
        maxShipLength: dock.maxShipLength ?? null,
        isActive: dock.isActive ?? true
      };
      this.docksMap.set(id, dockWithId);
    });
  }



  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.usersMap.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.usersMap.values()).find(user => user.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.usersMap.values()).find(user => user.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userIdCounter++;
    const now = new Date();
    
    // First user becomes admin automatically
    const isFirstUser = this.usersMap.size === 0;
    
    const user: User = { 
      ...insertUser, 
      id, 
      createdAt: now,
      phoneNumber: insertUser.phoneNumber ?? null,
      isAdmin: isFirstUser || insertUser.isAdmin || false,
      language: insertUser.language ?? 'de'
    };
    this.usersMap.set(id, user);
    
    if (isFirstUser) {
      console.log(`First user ${insertUser.username} (${insertUser.email}) automatically promoted to admin`);
    }
    
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const user = await this.getUser(id);
    if (!user) return undefined;

    const updatedUser = { ...user, ...userData };
    this.usersMap.set(id, updatedUser);
    return updatedUser;
  }

  // Ship operations
  async getShip(id: number): Promise<Ship | undefined> {
    return this.shipsMap.get(id);
  }

  async getShipsByUser(userId: number): Promise<Ship[]> {
    return Array.from(this.shipsMap.values()).filter(
      (ship) => ship.userId === userId
    );
  }

  async createShip(insertShip: InsertShip): Promise<Ship> {
    const id = this.shipIdCounter++;
    const now = new Date();
    const ship: Ship = { 
      ...insertShip, 
      id, 
      createdAt: now,
      length: insertShip.length ?? null,
      maxPassengers: insertShip.maxPassengers ?? null
    };
    this.shipsMap.set(id, ship);
    return ship;
  }

  async updateShip(id: number, shipData: Partial<Ship>): Promise<Ship | undefined> {
    const ship = await this.getShip(id);
    if (!ship) return undefined;

    const updatedShip = { ...ship, ...shipData };
    this.shipsMap.set(id, updatedShip);
    return updatedShip;
  }

  async deleteShip(id: number): Promise<boolean> {
    return this.shipsMap.delete(id);
  }

  // Dock operations
  async getDock(id: number): Promise<Dock | undefined> {
    return this.docksMap.get(id);
  }

  async getAllDocks(): Promise<Dock[]> {
    return Array.from(this.docksMap.values());
  }

  async resetDocks(): Promise<void> {
    this.initializeDocks();
  }

  async createDock(insertDock: InsertDock): Promise<Dock> {
    const id = this.dockIdCounter++;
    const dock: Dock = { 
      ...insertDock, 
      id,
      maxShipLength: insertDock.maxShipLength ?? null,
      isActive: insertDock.isActive ?? true
    };
    this.docksMap.set(id, dock);
    return dock;
  }

  // Booking operations
  async getBooking(id: number): Promise<BookingWithDetails | undefined> {
    const booking = this.bookingsMap.get(id);
    if (!booking) return undefined;
    return this.enrichBooking(booking);
  }

  async getBookingsByUser(userId: number): Promise<BookingWithDetails[]> {
    const bookings = Array.from(this.bookingsMap.values()).filter(
      (booking) => booking.userId === userId
    );
    return Promise.all(bookings.map(booking => this.enrichBooking(booking)));
  }

  private async enrichBooking(booking: Booking): Promise<BookingWithDetails> {
    const ship = await this.getShip(booking.shipId);
    const dock = await this.getDock(booking.dockId);
    const user = await this.getUser(booking.userId);
    
    if (!ship || !dock || !user) {
      throw new Error(`Missing related data for booking ${booking.id}`);
    }

    const { password, ...userWithoutPassword } = user;
    
    return {
      ...booking,
      ship,
      dock,
      user: userWithoutPassword
    };
  }

  async createBooking(insertBooking: InsertBooking): Promise<Booking> {
    const id = this.bookingIdCounter++;
    const now = new Date();
    const booking: Booking = { 
      ...insertBooking, 
      id, 
      createdAt: now,
      // Sicherstellen, dass alle optionalen Felder korrekt behandelt werden
      embarking: insertBooking.embarking ?? null,
      disembarking: insertBooking.disembarking ?? null,
      embarkingDisembarking: insertBooking.embarkingDisembarking ?? null,
      loadingDisposal: insertBooking.loadingDisposal ?? null,
      excursionWithBus: insertBooking.excursionWithBus ?? null,
      excursionWithoutBus: insertBooking.excursionWithoutBus ?? null,
      routeFrom: insertBooking.routeFrom ?? null,
      routeTo: insertBooking.routeTo ?? null,
      row: insertBooking.row ?? null,
      notes: insertBooking.notes ?? null
    };
    this.bookingsMap.set(id, booking);
    return booking;
  }

  async updateBooking(id: number, bookingData: Partial<Booking>): Promise<Booking | undefined> {
    const booking = this.bookingsMap.get(id);
    if (!booking) return undefined;

    const updatedBooking = { ...booking, ...bookingData };
    this.bookingsMap.set(id, updatedBooking);
    return updatedBooking;
  }

  async deleteBooking(id: number): Promise<boolean> {
    return this.bookingsMap.delete(id);
  }

  // Admin settings operations
  async getAdminSettings(): Promise<AdminSettings | undefined> {
    // Return default settings for memory storage
    return {
      id: 1,
      editTimeLimit: 24,
      requireAdminApproval: true,
      allowSelfEdit: false
    };
  }

  async updateAdminSettings(settings: Partial<AdminSettings>): Promise<AdminSettings> {
    // For memory storage, just return the settings as if updated
    return {
      id: 1,
      editTimeLimit: settings.editTimeLimit ?? 24,
      requireAdminApproval: settings.requireAdminApproval ?? true,
      allowSelfEdit: settings.allowSelfEdit ?? false
    };
  }

  // Booking edit request operations (simplified for memory storage)
  async createBookingEditRequest(request: InsertBookingEditRequest): Promise<BookingEditRequest> {
    return {
      id: 1,
      ...request,
      status: 'pending',
      createdAt: new Date(),
      reviewedAt: null,
      reviewedBy: null,
      adminNotes: null
    };
  }

  async getBookingEditRequests(): Promise<BookingEditRequestWithDetails[]> {
    return [];
  }

  async getBookingEditRequest(id: number): Promise<BookingEditRequestWithDetails | undefined> {
    return undefined;
  }

  async updateBookingEditRequestStatus(id: number, status: string, adminNotes?: string, reviewedBy?: number): Promise<BookingEditRequest | undefined> {
    return undefined;
  }

  // Admin operations
  async getAllShipsForAdmin(): Promise<(Ship & { user: Omit<User, 'password'> })[]> {
    const allShips = Array.from(this.shipsMap.values());
    const result = [];

    for (const ship of allShips) {
      const user = await this.getUser(ship.userId);
      if (user) {
        const { password, ...userWithoutPassword } = user;
        result.push({
          ...ship,
          user: userWithoutPassword
        });
      }
    }

    return result;
  }

  async getAllUsers(): Promise<Omit<User, 'password'>[]> {
    const allUsers = Array.from(this.usersMap.values());
    return allUsers.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.usersMap.delete(id);
  }

  // Replit Auth operations (not used but required by interface)
  async getReplitUser(id: string): Promise<User | undefined> {
    return undefined;
  }

  async upsertUser(userData: any): Promise<User> {
    const user: User = {
      id: this.userIdCounter++,
      username: userData.sub || 'user',
      password: '',
      companyName: userData.first_name + ' ' + userData.last_name || 'Unknown',
      email: userData.email || '',
      phoneNumber: userData.phoneNumber || null,
      language: 'de',
      isAdmin: false,
      createdAt: new Date()
    };

    this.usersMap.set(user.id, user);
    return user;
  }
}

// DatabaseStorage implementation for PostgreSQL
export class DatabaseStorage implements IStorage {
  public sessionStore: session.Store;

  constructor() {
    const PostgresSessionStore = connectPg(session);
    this.sessionStore = new PostgresSessionStore({ 
      pool, 
      createTableIfMissing: true 
    });
    
    // Initialize with some docks
    this.initializeDocks();
  }

  // Initialize predefined docks
  private async initializeDocks() {
    try {
      // Check if docks already exist
      const existingDocks = await db.select().from(docks);
      
      if (existingDocks.length === 0) {
        // Add predefined docks only if there are none
        const predefinedDocks: InsertDock[] = [
          { name: 'Engelhartszell', location: 'Engelhartszell', maxShipLength: 135, isActive: true },
          { name: 'Wesenufer', location: 'Wesenufer', maxShipLength: 120, isActive: true },
          { name: 'Schlögen', location: 'Schlögen', maxShipLength: 110, isActive: true },
          { name: 'Obermühl', location: 'Obermühl', maxShipLength: 100, isActive: true },
          { name: 'Untermühl', location: 'Untermühl', maxShipLength: 100, isActive: true },
          { name: 'Aschach', location: 'Aschach', maxShipLength: 135, isActive: true },
          { name: 'Brandstatt', location: 'Brandstatt', maxShipLength: 110, isActive: true },
          { name: 'Ottensheim', location: 'Ottensheim', maxShipLength: 135, isActive: true },
          { name: 'Linz', location: 'Linz', maxShipLength: 150, isActive: true },
          { name: 'Mauthausen', location: 'Mauthausen', maxShipLength: 135, isActive: true },
          { name: 'Enns', location: 'Enns', maxShipLength: 120, isActive: true },
          { name: 'Grein', location: 'Grein', maxShipLength: 110, isActive: true },
          { name: 'Sarmingstein', location: 'Sarmingstein', maxShipLength: 100, isActive: true }
        ];
        
        for (const dock of predefinedDocks) {
          await this.createDock(dock);
        }
        console.log('Initialized predefined docks');
      }
    } catch (error) {
      console.error('Error initializing docks:', error);
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    // Check if this is the first user - if so, make them admin
    const existingUsers = await db.select().from(users);
    const isFirstUser = existingUsers.length === 0;
    
    const userData = {
      ...insertUser,
      isAdmin: isFirstUser || insertUser.isAdmin || false
    };
    
    const [user] = await db
      .insert(users)
      .values(userData)
      .returning();
      
    if (isFirstUser) {
      console.log(`First user ${insertUser.username} (${insertUser.email}) automatically promoted to admin`);
    }
    
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const [user] = await db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning();
    return user;
  }

  // Ship operations
  async getShip(id: number): Promise<Ship | undefined> {
    const [ship] = await db.select().from(ships).where(eq(ships.id, id));
    return ship;
  }

  async getShipsByUser(userId: number): Promise<Ship[]> {
    return await db.select().from(ships).where(eq(ships.userId, userId));
  }

  async createShip(insertShip: InsertShip): Promise<Ship> {
    const [ship] = await db
      .insert(ships)
      .values(insertShip)
      .returning();
    return ship;
  }

  async updateShip(id: number, shipData: Partial<Ship>): Promise<Ship | undefined> {
    const [ship] = await db
      .update(ships)
      .set(shipData)
      .where(eq(ships.id, id))
      .returning();
    return ship;
  }

  async deleteShip(id: number): Promise<boolean> {
    const result = await db.delete(ships).where(eq(ships.id, id));
    return result.rowCount ? result.rowCount > 0 : false;
  }

  // Dock operations
  async getDock(id: number): Promise<Dock | undefined> {
    const [dock] = await db.select().from(docks).where(eq(docks.id, id));
    return dock;
  }

  async getAllDocks(): Promise<Dock[]> {
    return await db.select().from(docks);
  }

  async createDock(insertDock: InsertDock): Promise<Dock> {
    const [dock] = await db
      .insert(docks)
      .values(insertDock)
      .returning();
    return dock;
  }

  // Booking operations
  async getBooking(id: number): Promise<BookingWithDetails | undefined> {
    const [booking] = await db.select().from(bookings).where(eq(bookings.id, id));
    if (!booking) return undefined;
    return await this.enrichBooking(booking);
  }

  async getBookingsByUser(userId: number): Promise<BookingWithDetails[]> {
    const userBookings = await db.select().from(bookings).where(eq(bookings.userId, userId));
    return await Promise.all(userBookings.map(booking => this.enrichBooking(booking)));
  }

  private async enrichBooking(booking: Booking): Promise<BookingWithDetails> {
    const ship = await this.getShip(booking.shipId);
    const dock = await this.getDock(booking.dockId);
    const user = await this.getUser(booking.userId);
    
    return {
      ...booking,
      ship: ship!,
      dock: dock!,
      user: { ...user!, password: undefined } as Omit<User, 'password'>
    };
  }

  async createBooking(insertBooking: InsertBooking): Promise<Booking> {
    const [booking] = await db
      .insert(bookings)
      .values(insertBooking)
      .returning();
    return booking;
  }

  async updateBooking(id: number, bookingData: Partial<Booking>): Promise<Booking | undefined> {
    const [booking] = await db
      .update(bookings)
      .set(bookingData)
      .where(eq(bookings.id, id))
      .returning();
    return booking;
  }

  async deleteBooking(id: number): Promise<boolean> {
    const result = await db.delete(bookings).where(eq(bookings.id, id));
    return result.rowCount ? result.rowCount > 0 : false;
  }

  // Admin settings operations
  async getAdminSettings(): Promise<AdminSettings | undefined> {
    const [settings] = await db.select().from(adminSettings);
    return settings;
  }

  async updateAdminSettings(settingsData: Partial<AdminSettings>): Promise<AdminSettings> {
    const existing = await this.getAdminSettings();
    
    if (existing) {
      const [updated] = await db
        .update(adminSettings)
        .set({ ...settingsData, updatedAt: new Date() })
        .where(eq(adminSettings.id, existing.id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(adminSettings)
        .values({
          editingCutoffTime: null,
          editingStartTime: null,
          editingEndTime: null,
          adminEmail: null,
          ...settingsData
        })
        .returning();
      return created;
    }
  }

  // Booking edit request operations
  async createBookingEditRequest(request: InsertBookingEditRequest): Promise<BookingEditRequest> {
    const [editRequest] = await db
      .insert(bookingEditRequests)
      .values(request)
      .returning();
    return editRequest;
  }

  async getBookingEditRequests(): Promise<BookingEditRequestWithDetails[]> {
    const requests = await db.select().from(bookingEditRequests);
    return await Promise.all(requests.map(async request => {
      const booking = await this.getBooking(request.bookingId);
      const user = await this.getUser(request.userId);
      return {
        ...request,
        booking: booking!,
        user: { ...user!, password: undefined } as Omit<User, 'password'>
      };
    }));
  }

  async getBookingEditRequest(id: number): Promise<BookingEditRequestWithDetails | undefined> {
    const [request] = await db.select().from(bookingEditRequests).where(eq(bookingEditRequests.id, id));
    if (!request) return undefined;
    
    const booking = await this.getBooking(request.bookingId);
    const user = await this.getUser(request.userId);
    
    return {
      ...request,
      booking: booking!,
      user: { ...user!, password: undefined } as Omit<User, 'password'>
    };
  }

  async updateBookingEditRequestStatus(
    id: number, 
    status: string, 
    adminNotes?: string, 
    reviewedBy?: number
  ): Promise<BookingEditRequest | undefined> {
    const [request] = await db
      .update(bookingEditRequests)
      .set({
        status,
        adminNotes: adminNotes || null,
        reviewedBy: reviewedBy || null,
        reviewedAt: new Date()
      })
      .where(eq(bookingEditRequests.id, id))
      .returning();
    return request;
  }

  // Admin ship operations
  async getAllShipsForAdmin(): Promise<(Ship & { user: Omit<User, 'password'> })[]> {
    const allShips = await db.select().from(ships);
    return await Promise.all(allShips.map(async ship => {
      const user = await this.getUser(ship.userId);
      return {
        ...ship,
        user: { ...user!, password: undefined } as Omit<User, 'password'>
      };
    }));
  }

  // Admin user operations
  async getAllUsers(): Promise<Omit<User, 'password'>[]> {
    const allUsers = await db.select().from(users);
    return allUsers.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });
  }

  async deleteUser(id: number): Promise<boolean> {
    const result = await db.delete(users).where(eq(users.id, id));
    return result.rowCount ? result.rowCount > 0 : false;
  }

  // Replit Auth user operations (legacy compatibility)
  async getReplitUser(id: string): Promise<User | undefined> {
    return undefined; // Not used in email/password auth
  }

  async upsertUser(userData: any): Promise<User> {
    // Not used in email/password auth
    throw new Error("upsertUser not supported in email/password auth");
  }
}

export const storage = new DatabaseStorage();