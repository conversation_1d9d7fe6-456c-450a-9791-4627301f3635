2025-06-06 07:50:32,063 - semgrep.notifications - WARNING - METRICS: Using configs from the Registry (like --config=p/ci) reports pseudonymous rule metrics to semgrep.dev.
To disable Registry rule metrics, use "--metrics=off".
Using configs only from local files (like --config=xyz.yml) does not enable metrics.

More information: https://semgrep.dev/docs/metrics

2025-06-06 07:50:32,065 - semgrep.run_scan - DEBUG - semgrep version 1.2.0
2025-06-06 07:50:32,068 - semgrep.git - DEBUG - Failed to get project url from 'git ls-remote': Command failed with exit code: 128
-----
Command failed with output:
fatal: No remote configured to list refs from.


Failed to run 'git ls-remote --get-url'. Possible reasons:

- the git binary is not available
- the current working directory is not a git repository
- the baseline commit is not a parent of the current commit
    (if you are running through semgrep-app, check if you are setting `SEMGREP_BRANCH` or `SEMGREP_BASELINE_COMMIT` properly)
- the current working directory is not marked as safe
    (fix with `git config --global --add safe.directory $(pwd)`)

Try running the command yourself to debug the issue.
2025-06-06 07:50:32,068 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-06 07:50:32,070 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-06 07:50:32,073 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-nx3cgb9d.rules
2025-06-06 07:50:32,214 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIIR9rzI/semgrep/bin/semgrep-core-proprietary'.
2025-06-06 07:50:32,682 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-06-06 07:50:32,682 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-06-06 07:50:32,682 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-06-06 07:50:32,682 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.6138665676116943
2025-06-06 07:50:32,752 - semgrep.run_scan - VERBOSE - running 712 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-06-06 07:50:32,752 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-06-06 07:50:32,754 - semgrep.run_scan - VERBOSE - Rules:
2025-06-06 07:50:32,754 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-06-06 07:50:33,300 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-06-06 07:50:33,491 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-06-06 07:50:33,491 - semgrep.core_runner - DEBUG - /tmp/_MEIIR9rzI/semgrep/bin/opengrep-core -json -rules /tmp/tmpqst1tvyh.json -j 8 -targets /tmp/tmp7x2l2jp8 -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
2025-06-06 07:50:42,552 - semgrep.core_runner - DEBUG - --- semgrep-core stderr ---
[00.07][[34mINFO[0m]: Executed as: /tmp/_MEIIR9rzI/semgrep/bin/opengrep-core -json -rules /tmp/tmpqst1tvyh.json -j 8 -targets /tmp/tmp7x2l2jp8 -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
[00.07][[34mINFO[0m]: Version: 1.2.0
[00.07][[34mINFO[0m]: Parsing rules in /tmp/tmpqst1tvyh.json
[00.51][[34mINFO[0m]: scan: processing 371 files (skipping 0), with 454 rules (skipping 0 )
[01.33][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/auth-page.tsx func: AuthPage:978]
[0m[02.30][[31mERROR[0m]: exception on client/src/pages/admin-page-broken.tsx (Parsing_error.Syntax_error (client/src/pages/admin-page-broken.tsx:1:0 "import { useState } from \"react\";\nimport { useQuery, useMutation } from \"@tanstack/react-query\";\nimport { Settings, Clock, CheckCircle, XCircle, Calendar, Ship as ShipIcon, Users } from \"lucide-react\" ... (truncated)"))
[0m[03.68][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[03.99][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[04.10][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[04.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[04.11][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.40][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.47][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.49][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.49][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-fs-filename file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.76][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring file: client/src/components/booking-form.tsx func: BookingForm:25424]
[0m[05.85][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[05.90][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/booking-form.tsx func: BookingForm:25424]
[0m[06.38][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[06.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.remote-property-injection file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[07.15][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/components/booking-form.tsx func: BookingForm:25424]
[0m[07.41][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-insecure-template-usage file: client/src/components/booking-form.tsx func: BookingForm:25424]
[0m[07.41][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-ssrf file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[07.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[07.91][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[08.07][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/pages/admin-page.tsx func: AdminPage:13556]
[0m[09.02][[34mINFO[0m]: Custom ignore pattern: None
[09.02][[34mINFO[0m]: Custom ignore pattern: None
--- end semgrep-core stderr ---
2025-06-06 07:50:42,558 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0ee74fd49637bebe183eca7188dbde26e386314e62cc2e7ba1ee60b377b638243fcd84e6c6fa04886198ccacfa6a711bfbcc61a28f9ddc913d5b3c53083cbc90_0
2025-06-06 07:50:42,559 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-06 07:50:42,559 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-06 07:50:42,560 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-06 07:50:42,560 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.problem-based-packs.insecure-transport.js-node.bypass-tls-verification') match_id = ec9327650960f39bc1e6936e2557a864517c038ed6c1190e9c7616f785acc921f2ad3050f77d62a05faa1a67b7840432c7bd13cf511cf7ace9c3569344054207_0
2025-06-06 07:50:42,561 - semgrep.rule_match - DEBUG - match_key = ('process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;\n {rejectUnauthorized:false}\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.problem-based-packs.insecure-transport.js-node.bypass-tls-verification') match_id = d04d81ab6195bd0f9f00da96f0ebe57a6913906de5bf0aa0138a92239a3e43e5b5a4e5a63aac11728e7647d841d98310ff40e6e176d8c29400b0e76fe9b469c7_0
2025-06-06 07:50:42,561 - semgrep.rule_match - DEBUG - match_key = ('process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;\n {rejectUnauthorized:false}\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.problem-based-packs.insecure-transport.js-node.bypass-tls-verification') match_id = d04d81ab6195bd0f9f00da96f0ebe57a6913906de5bf0aa0138a92239a3e43e5b5a4e5a63aac11728e7647d841d98310ff40e6e176d8c29400b0e76fe9b469c7_0
2025-06-06 07:50:42,562 - semgrep.rule_match - DEBUG - match_key = ('process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;\n {rejectUnauthorized:false}\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.problem-based-packs.insecure-transport.js-node.bypass-tls-verification') match_id = d04d81ab6195bd0f9f00da96f0ebe57a6913906de5bf0aa0138a92239a3e43e5b5a4e5a63aac11728e7647d841d98310ff40e6e176d8c29400b0e76fe9b469c7_0
2025-06-06 07:50:42,562 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-06-06 07:50:42,563 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Telefon:</strong>  .*</?[a-zA-Z] `<p><strong>Telefon:</strong> ${requestedBy.phoneNumber}...`\n `...${requestedBy.phoneNumber}<p><strong>Telefon:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 240dcb747fa879d467fd7b67f2995bca22e8a9eb6e3fde17df75b8d1f2064d2938bc17746f45a80b389084e112bd647a72b1d676b708f7d5d28d808010ea9d36_0
2025-06-06 07:50:42,564 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Telefon:</strong>  .*</?[a-zA-Z] `<p><strong>Telefon:</strong> ${requestedBy.phoneNumber}...`\n `...${requestedBy.phoneNumber}<p><strong>Telefon:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 240dcb747fa879d467fd7b67f2995bca22e8a9eb6e3fde17df75b8d1f2064d2938bc17746f45a80b389084e112bd647a72b1d676b708f7d5d28d808010ea9d36_0
2025-06-06 07:50:42,564 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Telefon:</strong>  .*</?[a-zA-Z] `<p><strong>Telefon:</strong> ${requestedBy.phoneNumber}...`\n `...${requestedBy.phoneNumber}<p><strong>Telefon:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 240dcb747fa879d467fd7b67f2995bca22e8a9eb6e3fde17df75b8d1f2064d2938bc17746f45a80b389084e112bd647a72b1d676b708f7d5d28d808010ea9d36_0
2025-06-06 07:50:42,565 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-06-06 07:50:42,566 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Reihe:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Reihe:</strong> ${booking.row}...`\n `...${booking.row}<p><strong>Aktuelle Reihe:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 209ed14d77cd44e55d12e901c423864a82a2816d5abac9483fc826634dbc9772609f4c59d01a01c4689a2c165234c1332dc9de5b68e8f597b35be010b29d0ee3_0
2025-06-06 07:50:42,566 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Reihe:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Reihe:</strong> ${booking.row}...`\n `...${booking.row}<p><strong>Aktuelle Reihe:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 209ed14d77cd44e55d12e901c423864a82a2816d5abac9483fc826634dbc9772609f4c59d01a01c4689a2c165234c1332dc9de5b68e8f597b35be010b29d0ee3_0
2025-06-06 07:50:42,567 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Reihe:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Reihe:</strong> ${booking.row}...`\n `...${booking.row}<p><strong>Aktuelle Reihe:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 209ed14d77cd44e55d12e901c423864a82a2816d5abac9483fc826634dbc9772609f4c59d01a01c4689a2c165234c1332dc9de5b68e8f597b35be010b29d0ee3_0
2025-06-06 07:50:42,568 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-06-06 07:50:42,569 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Notizen:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Notizen:</strong> ${booking.notes}...`\n `...${booking.notes}<p><strong>Aktuelle Notizen:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ebf5282b28b003149ebde70ace96280f8942e9c53ddc817626557257fb4cfaadd2450e7c9fc34d1c121133ccad447ed23f72073bb8fe4f8d272bb4739f86997b_0
2025-06-06 07:50:42,569 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Notizen:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Notizen:</strong> ${booking.notes}...`\n `...${booking.notes}<p><strong>Aktuelle Notizen:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ebf5282b28b003149ebde70ace96280f8942e9c53ddc817626557257fb4cfaadd2450e7c9fc34d1c121133ccad447ed23f72073bb8fe4f8d272bb4739f86997b_0
2025-06-06 07:50:42,570 - semgrep.rule_match - DEBUG - match_key = ('<p><strong>Aktuelle Notizen:</strong>  .*</?[a-zA-Z] `<p><strong>Aktuelle Notizen:</strong> ${booking.notes}...`\n `...${booking.notes}<p><strong>Aktuelle Notizen:</strong> `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = ebf5282b28b003149ebde70ace96280f8942e9c53ddc817626557257fb4cfaadd2450e7c9fc34d1c121133ccad447ed23f72073bb8fe4f8d272bb4739f86997b_0
2025-06-06 07:50:42,571 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 55bb35191e06a876861fe164b021493592ca966e87c0c96d219cc5baff47a116f33c7fa591de72b60d7dfb1fc679bd457ad749a60a6c4f29461de8bcc7e28d6e_0
2025-06-06 07:50:42,572 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              </div>\n             .*</?[a-zA-Z] `</p>\n              </div>\n            ${reason}...`\n `...${reason}</p>\n              </div>\n            `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f409234b4ec5af499632a6a02f932ebc2b2b05cdbfd9c9386b8ef21a502f64ec2c567d08d6df0d34494e8f483cffa9130877b37da2b6f91ea3d4616bdcf9fd95_0
2025-06-06 07:50:42,573 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              </div>\n             .*</?[a-zA-Z] `</p>\n              </div>\n            ${reason}...`\n `...${reason}</p>\n              </div>\n            `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f409234b4ec5af499632a6a02f932ebc2b2b05cdbfd9c9386b8ef21a502f64ec2c567d08d6df0d34494e8f483cffa9130877b37da2b6f91ea3d4616bdcf9fd95_0
2025-06-06 07:50:42,573 - semgrep.rule_match - DEBUG - match_key = ('</p>\n              </div>\n             .*</?[a-zA-Z] `</p>\n              </div>\n            ${reason}...`\n `...${reason}</p>\n              </div>\n            `\n', PosixPath('server/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f409234b4ec5af499632a6a02f932ebc2b2b05cdbfd9c9386b8ef21a502f64ec2c567d08d6df0d34494e8f483cffa9130877b37da2b6f91ea3d4616bdcf9fd95_0
2025-06-06 07:50:42,576 - semgrep.core_runner - DEBUG - semgrep ran in 0:00:09.275749 on 137 files
2025-06-06 07:50:42,578 - semgrep.core_runner - DEBUG - findings summary: 6 warning, 0 error, 0 info
2025-06-06 07:50:42,580 - semgrep.app.auth - DEBUG - Getting API token from settings file
2025-06-06 07:50:42,580 - semgrep.app.auth - DEBUG - No API token found in settings file
2025-06-06 07:50:42,581 - semgrep.semgrep_core - DEBUG - Failed to open resource semgrep-core-proprietary: [Errno 2] No such file or directory: '/tmp/_MEIIR9rzI/semgrep/bin/semgrep-core-proprietary'.
2025-06-06 07:50:42,704 - semgrep.output - VERBOSE - 
========================================
Files skipped:
========================================

  Always skipped by Opengrep:

   • <none>

  Skipped by .gitignore:
  (Disable by passing --no-git-ignore)

   • <all files not listed by `git ls-files` were skipped>

  Skipped by .semgrepignore:
  - https://semgrep.dev/docs/ignoring-files-folders-code/#understand-semgrep-defaults

   • <none>

  Skipped by --include patterns:

   • <none>

  Skipped by --exclude patterns:

   • <none>

  Files skipped due to insufficient read permissions:

   • <none>

  Skipped by limiting to files smaller than 1000000 bytes:
  (Adjust with the --max-target-bytes flag)

   • <none>

  Partially analyzed due to parsing or internal Opengrep errors

   • client/src/pages/admin-page-broken.tsx
   • tailwind.config.ts (1 lines skipped)

2025-06-06 07:50:42,706 - semgrep.output - INFO - Some files were skipped or only partially analyzed.
  Scan was limited to files tracked by git.
  Partially scanned: 2 files only partially analyzed due to parsing or internal Opengrep errors

Ran 435 rules on 137 files: 6 findings.
2025-06-06 07:50:42,706 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-06 07:50:42,725 - semgrep.metrics - VERBOSE - Not sending pseudonymous metrics since metrics are configured to OFF and registry usage is False
