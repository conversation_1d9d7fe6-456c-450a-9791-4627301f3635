# Ship Docking Management System - API Documentation

## Overview
This document describes the REST API endpoints for the Ship Docking Management System.

## Base URL
All API endpoints are prefixed with `/api`

## Authentication
The API uses session-based authentication with Passport.js. Users must be logged in to access protected endpoints.

## Response Format
All responses are in JSON format with appropriate HTTP status codes.

### Success Response
```json
{
  "data": "response_data",
  "status": "success"
}
```

### Error Response
```json
{
  "message": "Error description",
  "status": "error"
}
```

## Authentication Endpoints

### Register User
```http
POST /api/register
```

**Request Body:**
```json
{
  "username": "string (required)",
  "password": "string (required)",
  "email": "string (required)",
  "companyName": "string (required)",
  "phoneNumber": "string (optional)",
  "language": "de|en (required)"
}
```

**Response:** `201 Created`
```json
{
  "id": 1,
  "username": "example_user",
  "email": "<EMAIL>",
  "companyName": "Example Company",
  "phoneNumber": "+43123456789",
  "language": "de",
  "isAdmin": false,
  "createdAt": "2025-06-06T10:00:00.000Z"
}
```

### Login User
```http
POST /api/login
```

**Request Body:**
```json
{
  "username": "string (required)",
  "password": "string (required)"
}
```

**Response:** `200 OK`
```json
{
  "id": 1,
  "username": "example_user",
  "email": "<EMAIL>",
  "companyName": "Example Company",
  "isAdmin": false
}
```

### Logout User
```http
POST /api/logout
```

**Response:** `200 OK`

### Get Current User
```http
GET /api/user
```

**Response:** `200 OK`
```json
{
  "id": 1,
  "username": "example_user",
  "email": "<EMAIL>",
  "companyName": "Example Company",
  "phoneNumber": "+43123456789",
  "language": "de",
  "isAdmin": false,
  "createdAt": "2025-06-06T10:00:00.000Z"
}
```

## Ship Management

### Get User's Ships
```http
GET /api/ships
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "name": "Ship Name",
    "userId": 1,
    "length": 110.5,
    "width": 11.4,
    "entryHeight1": 4.5,
    "entryHeight2": 4.2,
    "sundeckEntryHeight": 6.8,
    "entryDistanceFromBow": 15.0,
    "maxPassengers": 180,
    "createdAt": "2025-06-06T10:00:00.000Z"
  }
]
```

### Create Ship
```http
POST /api/ships
```

**Request Body:**
```json
{
  "name": "string (required)",
  "length": "number (optional)",
  "width": "number (optional)",
  "entryHeight1": "number (optional)",
  "entryHeight2": "number (optional)",
  "sundeckEntryHeight": "number (optional)",
  "entryDistanceFromBow": "number (optional)",
  "maxPassengers": "number (optional)"
}
```

**Response:** `201 Created` - Returns created ship object

### Update Ship
```http
PUT /api/ships/:id
```

**Request Body:** Same as create ship (partial updates allowed)

**Response:** `200 OK` - Returns updated ship object

### Delete Ship
```http
DELETE /api/ships/:id
```

**Response:** `204 No Content`

## Dock Management

### Get All Docks
```http
GET /api/docks
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "name": "Linz",
    "location": "Linz",
    "maxShipLength": 135.0,
    "isActive": true
  }
]
```

## Booking Management

### Get User's Bookings
```http
GET /api/bookings
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "userId": 1,
    "shipId": 1,
    "dockId": 1,
    "arrivalDate": "2025-06-15T10:00:00.000Z",
    "departureDate": "2025-06-15T18:00:00.000Z",
    "status": "pending",
    "embarking": "50",
    "disembarking": "50",
    "embarkingDisembarking": "Transfer passengers",
    "loadingDisposal": "Supplies",
    "excursionWithBus": true,
    "excursionWithoutBus": false,
    "routeFrom": "Vienna",
    "routeTo": "Passau",
    "row": "A1",
    "notes": "Special requirements",
    "createdAt": "2025-06-06T10:00:00.000Z",
    "ship": {
      "id": 1,
      "name": "Ship Name",
      "length": 110.5
    },
    "dock": {
      "id": 1,
      "name": "Linz",
      "location": "Linz"
    },
    "user": {
      "id": 1,
      "username": "example_user",
      "companyName": "Example Company"
    }
  }
]
```

### Get Single Booking
```http
GET /api/bookings/:id
```

**Response:** `200 OK` - Returns booking object with ship, dock, and user details

### Create Booking
```http
POST /api/bookings
```

**Request Body:**
```json
{
  "shipId": "number (required)",
  "dockId": "number (required)",
  "arrivalDate": "string (ISO date, required)",
  "arrivalTime": "string (HH:MM, required)",
  "departureDate": "string (ISO date, required)",
  "departureTime": "string (HH:MM, required)",
  "status": "string (default: pending)",
  "embarking": "string (optional)",
  "disembarking": "string (optional)",
  "embarkingDisembarking": "string (optional)",
  "loadingDisposal": "string (optional)",
  "excursionWithBus": "boolean (optional)",
  "excursionWithoutBus": "boolean (optional)",
  "routeFrom": "string (optional)",
  "routeTo": "string (optional)",
  "notes": "string (optional)"
}
```

**Response:** `201 Created` - Returns created booking object

### Update Booking
```http
PUT /api/bookings/:id
```

**Request Body:** Same as create booking (partial updates allowed)

**Response:** `200 OK` - Returns updated booking object

### Delete Booking
```http
DELETE /api/bookings/:id
```

**Response:** `204 No Content`

### Check Edit Permissions
```http
GET /api/bookings/:id/can-edit
```

**Response:** `200 OK`
```json
{
  "canEdit": true,
  "requiresRequest": false
}
```

## Change Request Management

### Create Change Request
```http
POST /api/booking-edit-requests
```

**Request Body:**
```json
{
  "bookingId": "number (required)",
  "requestedChanges": "string (JSON, required)",
  "reason": "string (optional)"
}
```

**Response:** `201 Created` - Returns created change request object

## Admin Endpoints

All admin endpoints require admin privileges.

### Get Admin Settings
```http
GET /api/admin/settings
```

**Response:** `200 OK`
```json
{
  "id": 1,
  "editingCutoffTime": "2025-06-15T12:00:00.000Z",
  "editingStartTime": "2025-06-20T08:00:00.000Z",
  "editingEndTime": "2025-06-22T18:00:00.000Z",
  "adminEmail": "<EMAIL>",
  "createdAt": "2025-06-06T10:00:00.000Z",
  "updatedAt": "2025-06-06T10:00:00.000Z"
}
```

### Update Admin Settings
```http
PUT /api/admin/settings
```

**Request Body:**
```json
{
  "editingCutoffTime": "string (ISO date, optional)",
  "editingStartTime": "string (ISO date, optional)",
  "editingEndTime": "string (ISO date, optional)",
  "adminEmail": "string (optional)"
}
```

**Response:** `200 OK` - Returns updated settings object

### Get All Users
```http
GET /api/admin/users
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "username": "example_user",
    "email": "<EMAIL>",
    "companyName": "Example Company",
    "phoneNumber": "+43123456789",
    "language": "de",
    "isAdmin": false,
    "createdAt": "2025-06-06T10:00:00.000Z"
  }
]
```

### Delete User
```http
DELETE /api/admin/users/:id
```

**Response:** `204 No Content`

### Get All Ships (Admin)
```http
GET /api/admin/ships
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "name": "Ship Name",
    "userId": 1,
    "length": 110.5,
    "maxPassengers": 180,
    "createdAt": "2025-06-06T10:00:00.000Z",
    "user": {
      "id": 1,
      "username": "example_user",
      "companyName": "Example Company"
    }
  }
]
```

### Get Change Requests
```http
GET /api/admin/change-requests
```

**Response:** `200 OK`
```json
[
  {
    "id": 1,
    "userId": 1,
    "bookingId": 1,
    "requestedChanges": "{\"dockId\":2}",
    "reason": "Need to change dock",
    "status": "pending",
    "adminNotes": null,
    "reviewedBy": null,
    "reviewedAt": null,
    "createdAt": "2025-06-06T10:00:00.000Z",
    "booking": {
      "id": 1,
      "ship": {"name": "Ship Name"},
      "dock": {"name": "Current Dock"}
    },
    "user": {
      "id": 1,
      "username": "example_user",
      "companyName": "Example Company"
    }
  }
]
```

### Update Change Request Status
```http
PUT /api/admin/change-requests/:id
```

**Request Body:**
```json
{
  "status": "approved|rejected (required)",
  "adminNotes": "string (optional)"
}
```

**Response:** `200 OK` - Returns updated change request object

## Error Codes

- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., username already exists)
- `500 Internal Server Error` - Server error

## Rate Limiting

The API does not currently implement rate limiting, but it's recommended to implement this in production environments.

## Data Validation

All endpoints validate input data using Zod schemas. Invalid data will return a `400 Bad Request` response with details about validation errors.

## Time Zones

All timestamps are stored and returned in UTC format (ISO 8601). Client applications should handle timezone conversion as needed.

## Pagination

Currently, the API does not implement pagination. All list endpoints return complete datasets. For large datasets, consider implementing pagination in future versions.