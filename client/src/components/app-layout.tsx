import { useState, ReactNode } from "react";
import { Link, useLocation } from "wouter";
import { 
  Ship, 
  LayoutDashboard, 
  Calendar, 
  UserCog, 
  LogOut, 
  Menu,
  Settings
} from "lucide-react";

import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface AppLayoutProps {
  children: ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const [location] = useLocation();
  const { user, logoutMutation } = useAuth();
  const { toast } = useToast();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // Get user initials for avatar
  const getUserInitials = (): string => {
    if (!user?.companyName) return "U";
    
    const words = user.companyName.split(" ");
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  const navItems = [
    { 
      href: "/", 
      label: "Dashboard", 
      icon: <LayoutDashboard size={20} /> 
    },
    { 
      href: "/bookings", 
      label: "Buchungen", 
      icon: <Calendar size={20} /> 
    },
    { 
      href: "/ships", 
      label: "Meine Schiffe", 
      icon: <Ship size={20} /> 
    },
    { 
      href: "/profile", 
      label: "Profil", 
      icon: <UserCog size={20} /> 
    },
    ...(user?.isAdmin ? [{ 
      href: "/admin", 
      label: "Admin", 
      icon: <Settings size={20} /> 
    }] : []),
  ];

  // Render a single navigation item based on whether it's active
  const NavItem = ({ item, isMobile = false }: { item: typeof navItems[0], isMobile?: boolean }) => {
    const isActive = location === item.href;
    const [, navigate] = useLocation();
    
    const handleClick = () => {
      if (isMobile) {
        setMobileMenuOpen(false);
      }
      navigate(item.href);
    };
    
    return (
      <li className="mb-1">
        <div
          className={cn(
            "flex items-center px-4 py-3 text-white w-full rounded-md cursor-pointer",
            isActive ? "bg-blue-900 font-semibold" : "hover:bg-blue-800",
            isMobile && "py-2"
          )}
          onClick={handleClick}
        >
          <span className="mr-3">{item.icon}</span>
          <span>{item.label}</span>
        </div>
      </li>
    );
  };

  // Render the logout button
  const LogoutButton = ({ isMobile = false }: { isMobile?: boolean }) => (
    <button 
      onClick={isMobile ? 
        () => { setMobileMenuOpen(false); handleLogout(); } : 
        handleLogout
      }
      className={cn(
        "flex items-center px-4 py-3 text-white w-full rounded-md text-left hover:bg-blue-800",
        isMobile && "py-2"
      )}
    >
      <span className="mr-3"><LogOut size={20} /></span>
      <span>Abmelden</span>
    </button>
  );

  return (
    <div className="flex flex-col md:flex-row min-h-screen bg-background">
      {/* Sidebar - Desktop */}
      <aside className="hidden md:flex md:w-64 bg-blue-800 md:flex-col md:min-h-screen">
        <div className="p-4 flex items-center">
          <Ship className="h-6 w-6 mr-2 text-white" />
          <h1 className="text-xl font-bold text-white">HafenBuchen</h1>
        </div>
        
        <div className="px-4 py-2 border-t border-blue-700">
          <p className="text-sm text-blue-300">Angemeldet als</p>
          <p className="font-medium text-white">{user?.companyName}</p>
        </div>
        
        <nav className="flex-1 mt-4 px-2">
          <ul>
            {navItems.map((item) => (
              <NavItem key={item.href} item={item} />
            ))}
            
            <li className="border-t border-blue-700 mt-4 pt-2">
              <LogoutButton />
            </li>
          </ul>
        </nav>
      </aside>

      {/* Mobile Header with Hamburger */}
      <header className="md:hidden bg-blue-800 p-4 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Ship className="h-5 w-5 text-white" />
          <h1 className="text-lg font-bold text-white">HafenBuchen</h1>
        </div>
        
        <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="text-white">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Open menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="bg-blue-800 p-0">
            <div className="p-4 flex items-center">
              <Ship className="h-6 w-6 mr-2 text-white" />
              <h1 className="text-xl font-bold text-white">HafenBuchen</h1>
            </div>
            
            <div className="px-4 py-2 border-t border-blue-700">
              <p className="text-sm text-blue-300">Angemeldet als</p>
              <p className="font-medium text-white">{user?.companyName}</p>
            </div>
            
            <nav className="mt-4 px-2">
              <ul>
                {navItems.map((item) => (
                  <NavItem key={item.href} item={item} isMobile />
                ))}
                
                <li className="border-t border-blue-700 mt-4 pt-2">
                  <LogoutButton isMobile />
                </li>
              </ul>
            </nav>
          </SheetContent>
        </Sheet>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex flex-col">
        {/* Top Navigation Bar */}
        <header className="bg-white shadow-sm z-10">
          <div className="flex items-center justify-between px-4 py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-800">
                {navItems.find(item => item.href === location)?.label || "Dashboard"}
              </h1>
            </div>
            <div>
              <Avatar>
                <AvatarFallback className="bg-primary text-white">
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <div className="flex-1 p-4 md:p-6 overflow-auto">
          {children}
        </div>
      </main>
    </div>
  );
}
