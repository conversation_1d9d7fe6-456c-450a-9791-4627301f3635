import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Ship } from "lucide-react";

import { InsertShip } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface ShipFormProps {
  shipId?: number;
  onSuccess?: () => void;
}

// Create schema for the form
const shipFormSchema = z.object({
  name: z.string().min(1, "Bitte geben Si<PERSON> einen Schiffsnamen ein"),
  length: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  maxPassengers: z.string().optional().transform(val => val ? parseInt(val) : undefined),
});

type ShipFormValues = z.infer<typeof shipFormSchema>;
// Typ für die Servereingabe
type ShipInput = {
  name: string;
  length?: number;
  maxPassengers?: number;
};

export default function ShipForm({ shipId, onSuccess }: ShipFormProps) {
  const { toast } = useToast();
  const isEditMode = !!shipId;

  // Fetch ship details if in edit mode
  const { data: ship, isLoading } = useQuery({
    queryKey: ["/api/ships", shipId],
    enabled: isEditMode,
  });

  // Form setup
  const form = useForm<ShipFormValues>({
    resolver: zodResolver(shipFormSchema),
    defaultValues: {
      name: "",
      length: "",
      maxPassengers: "",
    },
  });

  // Update form values when editing an existing ship
  useEffect(() => {
    if (ship && isEditMode) {
      form.reset({
        name: ship.name,
        length: ship.length?.toString() || "",
        maxPassengers: ship.maxPassengers?.toString() || "",
      });
    }
  }, [ship, isEditMode, form]);

  // Create ship mutation
  const createShipMutation = useMutation({
    mutationFn: async (data: ShipInput) => {
      // Die userId wird automatisch auf dem Server aus der Sitzung hinzugefügt
      const res = await apiRequest("POST", "/api/ships", data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ships"] });
      toast({
        title: "Schiff erstellt",
        description: "Das Schiff wurde erfolgreich erstellt.",
      });
      form.reset();
      if (onSuccess) onSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: `Fehler beim Erstellen des Schiffs: ${error.message}`,
        variant: "destructive",
      });
      console.error("Ship creation error:", error);
    },
  });

  // Update ship mutation
  const updateShipMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: ShipInput }) => {
      const res = await apiRequest("PUT", `/api/ships/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ships"] });
      if (shipId) {
        queryClient.invalidateQueries({ queryKey: ["/api/ships", shipId] });
      }
      toast({
        title: "Schiff aktualisiert",
        description: "Das Schiff wurde erfolgreich aktualisiert.",
      });
      if (onSuccess) onSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: `Fehler beim Aktualisieren des Schiffs: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Form submission handler
  function onSubmit(values: ShipFormValues) {
    if (isEditMode && shipId) {
      updateShipMutation.mutate({ id: shipId, data: values });
    } else {
      createShipMutation.mutate(values);
    }
  }

  if (isEditMode && isLoading) {
    return (
      <div className="p-8 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <Ship className="h-6 w-6 text-primary" />
        <h2 className="text-xl font-bold">
          {isEditMode ? "Schiff bearbeiten" : "Neues Schiff hinzufügen"}
        </h2>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Schiffsname</FormLabel>
                <FormControl>
                  <Input {...field} placeholder="z.B. Amadeus Nova" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="length"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Länge (in Metern)</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      {...field} 
                      placeholder="z.B. 135" 
                      min="0"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxPassengers"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max. Passagiere</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      {...field} 
                      placeholder="z.B. 150" 
                      min="0"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button 
              type="button" 
              variant="outline"
              onClick={() => {
                if (onSuccess) onSuccess();
              }}
            >
              Abbrechen
            </Button>
            <Button 
              type="submit" 
              disabled={createShipMutation.isPending || updateShipMutation.isPending}
            >
              {isEditMode ? "Schiff aktualisieren" : "Schiff hinzufügen"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
