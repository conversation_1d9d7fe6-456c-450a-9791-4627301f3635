import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery, useMutation } from "@tanstack/react-query";
import { CalendarIcon, Clock } from "lucide-react";
import { format } from "date-fns";

import { InsertBooking, BookingWithDetails, Ship, Dock } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { dateTimeToISOString, isoToDateString, isoToTimeString } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";
import { useLanguage } from "@/contexts/LanguageContext";
import BookingEditRequestForm from "@/components/booking-edit-request-form";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface BookingFormProps {
  bookingId?: number;
  onSuccess?: () => void;
}

// Create schema for the form
const bookingFormSchema = z.object({
  // Pflichtfelder
  shipId: z.string().min(1, "Bitte wählen Sie ein Schiff aus"),
  dockId: z.string().min(1, "Bitte wählen Sie eine Liegestelle aus"),
  arrivalDate: z.string().min(1, "Bitte wählen Sie ein Ankunftsdatum"),
  arrivalTime: z.string().min(1, "Bitte wählen Sie eine Ankunftszeit"),
  departureDate: z.string().min(1, "Bitte wählen Sie ein Abfahrtsdatum"),
  departureTime: z.string().min(1, "Bitte wählen Sie eine Abfahrtszeit"),
  status: z.string().default("pending"),
  
  // Optionale Felder
  embarking: z.string().optional(),
  disembarking: z.string().optional(), 
  embarkingDisembarking: z.string().optional(),
  loadingDisposal: z.string().optional(),
  excursionWithBus: z.boolean().optional(),
  excursionWithoutBus: z.boolean().optional(),
  routeFrom: z.string().optional(),
  routeTo: z.string().optional(),
  
  notes: z.string().optional(),
});

type BookingFormValues = z.infer<typeof bookingFormSchema>;

export default function BookingForm({ bookingId, onSuccess }: BookingFormProps) {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [isEditMode] = useState(!!bookingId);
  const [showRequestForm, setShowRequestForm] = useState(false);

  // Fetch ships
  const { data: ships = [] } = useQuery<Ship[]>({
    queryKey: ["/api/ships"],
  });

  // Fetch docks
  const { data: docks = [] } = useQuery<Dock[]>({
    queryKey: ["/api/docks"],
  });

  // Fetch booking details if in edit mode
  const { data: booking, isLoading: isLoadingBooking } = useQuery<BookingWithDetails>({
    queryKey: ["/api/bookings", bookingId],
    enabled: !!bookingId,
  });

  // Check if editing is allowed for this booking
  const { data: editPermission, isLoading: isLoadingPermission, error: permissionError } = useQuery<{ canEdit: boolean; requiresRequest: boolean }>({
    queryKey: [`/api/bookings/${bookingId}/can-edit`],
    enabled: !!bookingId && isEditMode,
  });



  // Form setup
  const form = useForm<BookingFormValues>({
    resolver: zodResolver(bookingFormSchema),
    defaultValues: {
      shipId: "",
      dockId: "",
      arrivalDate: "",
      arrivalTime: "",
      departureDate: "",
      departureTime: "",
      status: "pending",
      embarking: "",
      disembarking: "",
      embarkingDisembarking: "",
      loadingDisposal: "",
      excursionWithBus: false,
      excursionWithoutBus: false,
      routeFrom: "",
      routeTo: "",
      notes: "",
    },
  });

  // Update form values when editing an existing booking
  useEffect(() => {
    if (booking && isEditMode) {
      console.log("Editing booking:", booking);
      try {
        form.reset({
          shipId: booking.shipId?.toString() || "",
          dockId: booking.dockId?.toString() || "",
          arrivalDate: isoToDateString(booking.arrivalDate),
          arrivalTime: isoToTimeString(booking.arrivalDate),
          departureDate: isoToDateString(booking.departureDate),
          departureTime: isoToTimeString(booking.departureDate),
          status: booking.status || "pending",
          embarking: booking.embarking || "",
          disembarking: booking.disembarking || "",
          embarkingDisembarking: booking.embarkingDisembarking || "",
          loadingDisposal: booking.loadingDisposal || "",
          excursionWithBus: booking.excursionWithBus || false,
          excursionWithoutBus: booking.excursionWithoutBus || false,
          routeFrom: booking.routeFrom || "",
          routeTo: booking.routeTo || "",
          notes: booking.notes || "",
        });
      } catch (error) {
        console.error("Error setting form values:", error);
      }
    }
  }, [booking, isEditMode, form]);

  // Create booking mutation
  const createBookingMutation = useMutation({
    mutationFn: async (data: InsertBooking) => {
      const res = await apiRequest("POST", "/api/bookings", data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      toast({
        title: "Buchung erstellt",
        description: "Die Buchung wurde erfolgreich erstellt.",
      });
      form.reset();
      if (onSuccess) onSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: `Fehler beim Erstellen der Buchung: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Update booking mutation
  const updateBookingMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<InsertBooking> }) => {
      const res = await apiRequest("PUT", `/api/bookings/${id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      if (bookingId) {
        queryClient.invalidateQueries({ queryKey: ["/api/bookings", bookingId] });
      }
      toast({
        title: "Buchung aktualisiert",
        description: "Die Buchung wurde erfolgreich aktualisiert.",
      });
      if (onSuccess) onSuccess();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: `Fehler beim Aktualisieren der Buchung: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Form submission handler
  function onSubmit(values: BookingFormValues) {
    // Parse date and time strings to create proper Date objects
    const [arrivalDay, arrivalMonth, arrivalYear] = values.arrivalDate.split('.');
    const [arrivalHour, arrivalMinute] = values.arrivalTime.split(':');
    
    // Create Date object
    const arrivalDate = new Date(
      parseInt(arrivalYear),
      parseInt(arrivalMonth) - 1,
      parseInt(arrivalDay),
      parseInt(arrivalHour),
      parseInt(arrivalMinute)
    );
    
    const [departureDay, departureMonth, departureYear] = values.departureDate.split('.');
    const [departureHour, departureMinute] = values.departureTime.split(':');
    const departureDate = new Date(
      parseInt(departureYear),
      parseInt(departureMonth) - 1,
      parseInt(departureDay),
      parseInt(departureHour),
      parseInt(departureMinute)
    );

    // Convert to ISO string format
    const arrivalISO = arrivalDate.toISOString();
    const departureISO = departureDate.toISOString();
    
    console.log("Sending dates:", arrivalISO, departureISO);

    const bookingData: Partial<InsertBooking> = {
      shipId: parseInt(values.shipId),
      dockId: parseInt(values.dockId),
      arrivalDate: arrivalDate,
      departureDate: departureDate,
      status: values.status,
      embarking: values.embarking || undefined,
      disembarking: values.disembarking || undefined,
      embarkingDisembarking: values.embarkingDisembarking || undefined,
      loadingDisposal: values.loadingDisposal || undefined,
      excursionWithBus: values.excursionWithBus || undefined,
      excursionWithoutBus: values.excursionWithoutBus || undefined,
      routeFrom: values.routeFrom || undefined,
      routeTo: values.routeTo || undefined,
      notes: values.notes || undefined,
    };

    if (isEditMode && bookingId) {
      updateBookingMutation.mutate({ id: bookingId, data: bookingData });
    } else {
      createBookingMutation.mutate(bookingData as InsertBooking);
    }
  }

  if (isEditMode && isLoadingBooking) {
    return (
      <div className="p-8 flex justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show request form if editing is not allowed
  if (isEditMode && editPermission && !editPermission.canEdit && booking && ships && docks) {
    if (showRequestForm) {
      return (
        <BookingEditRequestForm
          booking={booking}
          ships={ships}
          docks={docks}
          onSuccess={() => {
            setShowRequestForm(false);
            onSuccess?.();
          }}
          onCancel={() => setShowRequestForm(false)}
        />
      );
    }

    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="h-5 w-5 text-yellow-600" />
            <h3 className="font-medium text-yellow-800">Bearbeitungszeit abgelaufen</h3>
          </div>
          <p className="text-yellow-700 mb-4">
            Die Zeit für direkte Bearbeitungen ist abgelaufen. Sie können eine Bearbeitungsanfrage stellen, die von einem Admin genehmigt werden muss.
          </p>
          <Button 
            onClick={() => setShowRequestForm(true)}
            className="bg-yellow-600 hover:bg-yellow-700"
          >
            Bearbeitungsanfrage stellen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-h-[80vh] overflow-y-auto">
      <h2 className="text-xl font-bold mb-6">
        {isEditMode ? "Buchung bearbeiten" : "Neue Buchung erstellen"}
      </h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Ship and Location Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="shipId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('ship')} *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('ship')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ships.map((ship) => (
                        <SelectItem key={ship.id} value={ship.id.toString()}>
                          {ship.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="dockId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('dock')} *</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('dock')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {docks.map((dock) => (
                        <SelectItem key={dock.id} value={dock.id.toString()}>
                          {dock.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Arrival Date/Time */}
          <div>
            <h3 className="text-sm font-heading font-semibold text-gray-700 mb-3">{t('arrivalDate')} *</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="arrivalDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>{t('arrivalDate')} *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={`w-full pl-3 text-left font-normal ${
                              !field.value ? "text-muted-foreground" : ""
                            }`}
                          >
                            {field.value ? (
                              field.value
                            ) : (
                              <span>Datum auswählen</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date) => {
                            if (date) {
                              const day = String(date.getDate()).padStart(2, '0');
                              const month = String(date.getMonth() + 1).padStart(2, '0');
                              const year = date.getFullYear();
                              field.onChange(`${day}.${month}.${year}`);
                            }
                          }}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="arrivalTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('arrivalTime')} *</FormLabel>
                    <FormControl>
                      <Input type="time" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Departure Date/Time */}
          <div>
            <h3 className="text-sm font-heading font-semibold text-gray-700 mb-3">{t('departureDate')} *</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="departureDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Datum *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={`w-full pl-3 text-left font-normal ${
                              !field.value ? "text-muted-foreground" : ""
                            }`}
                          >
                            {field.value ? (
                              field.value
                            ) : (
                              <span>Datum auswählen</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          onSelect={(date) => {
                            if (date) {
                              const day = String(date.getDate()).padStart(2, '0');
                              const month = String(date.getMonth() + 1).padStart(2, '0');
                              const year = date.getFullYear();
                              field.onChange(`${day}.${month}.${year}`);
                            }
                          }}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="departureTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Uhrzeit *</FormLabel>
                    <FormControl>
                      <Input type="time" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Optional Service Information */}
          <div>
            <h3 className="text-sm font-heading font-semibold text-gray-700 mb-3">Optionale Angaben</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="embarking"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Einschiffung</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="z.B. Anzahl Passagiere" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="disembarking"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ausschiffung</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="z.B. Anzahl Passagiere" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="embarkingDisembarking"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ein-/Ausschiffung</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Kombinierte Angaben" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="loadingDisposal"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Beladung/Entsorgung</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Art der Ladung" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Excursion Options */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Ausflüge</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="excursionWithBus"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Ausflug mit Bus
                      </FormLabel>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="excursionWithoutBus"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        Ausflug ohne Bus
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Route Information */}
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Route</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="routeFrom"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Von</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Startort der Route" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="routeTo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nach</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Zielort der Route" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Status (visible only in edit mode) */}
          {isEditMode && (
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Status auswählen" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">Anfrage</SelectItem>
                      <SelectItem value="confirmed">Bestätigt</SelectItem>
                      <SelectItem value="cancelled">Storniert</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Additional Information */}
          <div>
            <h3 className="text-sm font-heading font-semibold text-gray-700 mb-3">Zusätzliche Informationen</h3>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Anmerkungen</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Besondere Anforderungen oder Informationen"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button 
              type="button" 
              variant="outline"
              onClick={() => {
                if (onSuccess) onSuccess();
              }}
            >
              Abbrechen
            </Button>
            <Button 
              type="submit" 
              disabled={createBookingMutation.isPending || updateBookingMutation.isPending}
            >
              {isEditMode ? "Buchung aktualisieren" : "Buchung erstellen"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
