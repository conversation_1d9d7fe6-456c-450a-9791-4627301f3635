import { createContext, useContext, ReactNode, useState, useEffect } from "react";
import { Language, t, TranslationKey } from "@/lib/i18n";
import { useAuth } from "@/hooks/useAuth";

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: TranslationKey) => string;
}

const LanguageContext = createContext<LanguageContextType | null>(null);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [language, setLanguageState] = useState<Language>('de');

  // Set language based on user preference when user data is available
  useEffect(() => {
    if (user?.language && (user.language === 'de' || user.language === 'en')) {
      setLanguageState(user.language as Language);
    }
  }, [user]);

  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
  };

  const translate = (key: TranslationKey) => {
    return t(key, language);
  };

  return (
    <LanguageContext.Provider
      value={{
        language,
        setLanguage,
        t: translate,
      }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}