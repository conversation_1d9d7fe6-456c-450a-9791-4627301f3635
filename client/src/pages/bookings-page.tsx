import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Plus, Filter, Search, CalendarSearch, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

import { BookingWithDetails, AdminSettings } from "@shared/schema";
import { formatDateTime } from "@/lib/date-utils";
import AppLayout from "@/components/app-layout";
import BookingForm from "@/components/booking-form";
import ConfirmDialog from "@/components/confirm-dialog";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type SortField = 'ship' | 'dock' | 'arrival' | 'departure' | 'status';
type SortDirection = 'asc' | 'desc';

export default function BookingsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedBookingId, setSelectedBookingId] = useState<number | null>(null);
  const [sortField, setSortField] = useState<SortField>('arrival');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Fetch bookings
  const { 
    data: bookings = [],
    isLoading,
    refetch
  } = useQuery<BookingWithDetails[]>({
    queryKey: ["/api/bookings"],
  });

  // Fetch admin settings to check editing restrictions
  const { data: adminSettings } = useQuery<AdminSettings>({
    queryKey: ["/api/admin/settings"],
  });

  // Check if editing is currently allowed
  const isEditingAllowed = () => {
    if (!adminSettings) return true;
    
    const now = new Date();
    
    // Check cutoff time restriction
    if (adminSettings.editingCutoffTime) {
      const cutoffTime = new Date(adminSettings.editingCutoffTime);
      if (now >= cutoffTime) return false;
    }
    
    // Check time range restriction
    if (adminSettings.editingStartTime && adminSettings.editingEndTime) {
      const startTime = new Date(adminSettings.editingStartTime);
      const endTime = new Date(adminSettings.editingEndTime);
      if (now >= startTime && now <= endTime) return false;
    }
    
    return true;
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort and filter bookings
  const sortedAndFilteredBookings = bookings
    .filter(booking => {
      const matchesSearch = 
        booking.ship.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.dock.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === "all" || booking.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue: string | Date;
      let bValue: string | Date;

      switch (sortField) {
        case 'ship':
          aValue = a.ship.name;
          bValue = b.ship.name;
          break;
        case 'dock':
          aValue = a.dock.name;
          bValue = b.dock.name;
          break;
        case 'arrival':
          aValue = new Date(a.arrivalDate);
          bValue = new Date(b.arrivalDate);
          break;
        case 'departure':
          aValue = new Date(a.departureDate);
          bValue = new Date(b.departureDate);
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = a.arrivalDate;
          bValue = b.arrivalDate;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  const handleDeleteClick = (bookingId: number) => {
    setSelectedBookingId(bookingId);
    setIsDeleteDialogOpen(true);
  };

  // Component for sortable table headers
  const SortableHeader = ({ field, children }: { field: SortField; children: React.ReactNode }) => {
    const isActive = sortField === field;
    const direction = isActive ? sortDirection : null;

    return (
      <th 
        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
        onClick={() => handleSort(field)}
      >
        <div className="flex items-center gap-2">
          {children}
          {isActive ? (
            direction === 'asc' ? (
              <ArrowUp className="h-3 w-3" />
            ) : (
              <ArrowDown className="h-3 w-3" />
            )
          ) : (
            <ArrowUpDown className="h-3 w-3 opacity-50" />
          )}
        </div>
      </th>
    );
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-2xl font-bold">Buchungen</h1>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" /> Neue Buchung
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <BookingForm onSuccess={() => refetch()} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Suche nach Schiff oder Ort..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Status</SelectItem>
                <SelectItem value="confirmed">Bestätigt</SelectItem>
                <SelectItem value="pending">Anfrage</SelectItem>
                <SelectItem value="cancelled">Storniert</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Bookings Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <SortableHeader field="ship">Schiff</SortableHeader>
                  <SortableHeader field="dock">Ort</SortableHeader>
                  <SortableHeader field="arrival">Ankunft</SortableHeader>
                  <SortableHeader field="departure">Abfahrt</SortableHeader>
                  <SortableHeader field="status">Status</SortableHeader>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aktionen
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center">
                      <div className="flex justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      </div>
                    </td>
                  </tr>
                ) : sortedAndFilteredBookings.length > 0 ? (
                  sortedAndFilteredBookings.map((booking) => (
                    <tr key={booking.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {booking.ship.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {booking.dock.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDateTime(booking.arrivalDate)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatDateTime(booking.departureDate)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={booking.status} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        {isEditingAllowed() ? (
                          <>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-primary h-auto py-1">
                                  Bearbeiten
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-3xl">
                                <BookingForm bookingId={booking.id} onSuccess={() => refetch()} />
                              </DialogContent>
                            </Dialog>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-destructive h-auto py-1"
                              onClick={() => handleDeleteClick(booking.id)}
                            >
                              Löschen
                            </Button>
                          </>
                        ) : (
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="text-orange-600 border-orange-300 h-auto py-1"
                              >
                                Bearbeitung anfragen
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-3xl">
                              <BookingForm bookingId={booking.id} onSuccess={() => refetch()} />
                            </DialogContent>
                          </Dialog>
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      <CalendarSearch className="h-10 w-10 mx-auto mb-2 text-gray-400" />
                      <p>Keine Buchungen gefunden.</p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        title="Buchung löschen"
        description="Sind Sie sicher, dass Sie diese Buchung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden."
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={async () => {
          if (selectedBookingId) {
            try {
              await fetch(`/api/bookings/${selectedBookingId}`, {
                method: "DELETE",
                credentials: "include",
              });
              refetch();
            } catch (error) {
              console.error("Error deleting booking:", error);
            }
          }
        }}
      />
    </AppLayout>
  );
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "confirmed":
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
          Bestätigt
        </Badge>
      );
    case "pending":
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          Anfrage
        </Badge>
      );
    case "cancelled":
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
          Storniert
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
          {status}
        </Badge>
      );
  }
}
