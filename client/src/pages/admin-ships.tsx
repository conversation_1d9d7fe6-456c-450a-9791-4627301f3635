import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Ship, User, Plus, Edit, Trash2 } from "lucide-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

interface ShipFormData {
  name: string;
  userId: string;
  length: string;
  width: string;
  entryHeight1: string;
  entryHeight2: string;
  sundeckEntryHeight: string;
  entryDistanceFromBow: string;
  maxPassengers: string;
}

export default function AdminShipsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingShip, setEditingShip] = useState<any>(null);

  const [formData, setFormData] = useState<ShipFormData>({
    name: "",
    userId: "",
    length: "",
    width: "",
    entryHeight1: "",
    entryHeight2: "",
    sundeckEntryHeight: "",
    entryDistanceFromBow: "",
    maxPassengers: "",
  });

  // Lade alle Schiffe für Admin
  const { data: ships = [], isLoading: shipsLoading } = useQuery({
    queryKey: ["/api/admin/ships"],
    enabled: user?.isAdmin,
  });

  // Lade alle Benutzer für die Firmenauswahl
  const { data: users = [], isLoading: usersLoading } = useQuery({
    queryKey: ["/api/admin/users"],
    enabled: user?.isAdmin,
  });

  const createShipMutation = useMutation({
    mutationFn: async (shipData: any) => {
      const res = await apiRequest("POST", "/api/admin/ships", shipData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/ships"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ships"] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: "Schiff erstellt",
        description: "Das Schiff wurde erfolgreich angelegt.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Erstellen des Schiffes",
        variant: "destructive",
      });
    },
  });

  const updateShipMutation = useMutation({
    mutationFn: async (shipData: any) => {
      const res = await apiRequest("PUT", `/api/admin/ships/${editingShip.id}`, shipData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/ships"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ships"] });
      setIsDialogOpen(false);
      setEditingShip(null);
      resetForm();
      toast({
        title: "Schiff aktualisiert",
        description: "Das Schiff wurde erfolgreich aktualisiert.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Aktualisieren des Schiffes",
        variant: "destructive",
      });
    },
  });

  const deleteShipMutation = useMutation({
    mutationFn: async (shipId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/ships/${shipId}`);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/ships"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ships"] });
      toast({
        title: "Schiff gelöscht",
        description: "Das Schiff wurde erfolgreich gelöscht.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Löschen des Schiffes",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: "",
      userId: "",
      length: "",
      width: "",
      entryHeight1: "",
      entryHeight2: "",
      sundeckEntryHeight: "",
      entryDistanceFromBow: "",
      maxPassengers: "",
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const shipData = {
      name: formData.name,
      userId: parseInt(formData.userId),
      length: formData.length ? parseInt(formData.length) : null,
      width: formData.width ? parseInt(formData.width) : null,
      entryHeight1: formData.entryHeight1 ? parseInt(formData.entryHeight1) : null,
      entryHeight2: formData.entryHeight2 ? parseInt(formData.entryHeight2) : null,
      sundeckEntryHeight: formData.sundeckEntryHeight ? parseInt(formData.sundeckEntryHeight) : null,
      entryDistanceFromBow: formData.entryDistanceFromBow ? parseInt(formData.entryDistanceFromBow) : null,
      maxPassengers: formData.maxPassengers ? parseInt(formData.maxPassengers) : null,
    };

    if (editingShip) {
      updateShipMutation.mutate(shipData);
    } else {
      createShipMutation.mutate(shipData);
    }
  };

  const handleEdit = (ship: any) => {
    setEditingShip(ship);
    setFormData({
      name: ship.name || "",
      userId: ship.userId?.toString() || "",
      length: ship.length?.toString() || "",
      width: ship.width?.toString() || "",
      entryHeight1: ship.entryHeight1?.toString() || "",
      entryHeight2: ship.entryHeight2?.toString() || "",
      sundeckEntryHeight: ship.sundeckEntryHeight?.toString() || "",
      entryDistanceFromBow: ship.entryDistanceFromBow?.toString() || "",
      maxPassengers: ship.maxPassengers?.toString() || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (shipId: number) => {
    if (confirm("Sind Sie sicher, dass Sie dieses Schiff löschen möchten?")) {
      deleteShipMutation.mutate(shipId);
    }
  };

  if (!user?.isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Sie haben keine Berechtigung für diese Seite.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Schiffsverwaltung</h1>
          <p className="text-muted-foreground">Verwalten Sie alle Schiffe im System</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => { resetForm(); setEditingShip(null); }}>
              <Plus className="h-4 w-4 mr-2" />
              Neues Schiff
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingShip ? "Schiff bearbeiten" : "Neues Schiff anlegen"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="name">Schiffsname *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    placeholder="Name des Schiffes"
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="userId">Firma *</Label>
                  <Select
                    value={formData.userId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, userId: value }))}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Firma auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map((user: any) => (
                        <SelectItem key={user.id} value={user.id.toString()}>
                          {user.companyName} ({user.username})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="length">Länge (m)</Label>
                  <Input
                    id="length"
                    type="number"
                    value={formData.length}
                    onChange={(e) => setFormData(prev => ({ ...prev, length: e.target.value }))}
                    placeholder="Länge in Metern"
                  />
                </div>
                <div>
                  <Label htmlFor="width">Breite (m)</Label>
                  <Input
                    id="width"
                    type="number"
                    value={formData.width}
                    onChange={(e) => setFormData(prev => ({ ...prev, width: e.target.value }))}
                    placeholder="Breite in Metern"
                  />
                </div>
                <div>
                  <Label htmlFor="entryHeight1">Einstiegshöhe 1 (cm)</Label>
                  <Input
                    id="entryHeight1"
                    type="number"
                    value={formData.entryHeight1}
                    onChange={(e) => setFormData(prev => ({ ...prev, entryHeight1: e.target.value }))}
                    placeholder="Höhe in Zentimetern"
                  />
                </div>
                <div>
                  <Label htmlFor="entryHeight2">Einstiegshöhe 2 (cm)</Label>
                  <Input
                    id="entryHeight2"
                    type="number"
                    value={formData.entryHeight2}
                    onChange={(e) => setFormData(prev => ({ ...prev, entryHeight2: e.target.value }))}
                    placeholder="Höhe in Zentimetern"
                  />
                </div>
                <div>
                  <Label htmlFor="sundeckEntryHeight">Einstiegshöhe Sonnendeck (cm)</Label>
                  <Input
                    id="sundeckEntryHeight"
                    type="number"
                    value={formData.sundeckEntryHeight}
                    onChange={(e) => setFormData(prev => ({ ...prev, sundeckEntryHeight: e.target.value }))}
                    placeholder="Höhe in Zentimetern"
                  />
                </div>
                <div>
                  <Label htmlFor="entryDistanceFromBow">Entfernung vom Bug (m)</Label>
                  <Input
                    id="entryDistanceFromBow"
                    type="number"
                    value={formData.entryDistanceFromBow}
                    onChange={(e) => setFormData(prev => ({ ...prev, entryDistanceFromBow: e.target.value }))}
                    placeholder="Entfernung in Metern"
                  />
                </div>
                <div>
                  <Label htmlFor="maxPassengers">Max. Passagiere</Label>
                  <Input
                    id="maxPassengers"
                    type="number"
                    value={formData.maxPassengers}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxPassengers: e.target.value }))}
                    placeholder="Maximale Passagieranzahl"
                  />
                </div>
              </div>
              <div className="flex gap-2 pt-4">
                <Button type="submit" disabled={createShipMutation.isPending || updateShipMutation.isPending}>
                  {editingShip ? "Aktualisieren" : "Erstellen"}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                >
                  Abbrechen
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {shipsLoading ? (
          <Card>
            <CardContent className="p-6">
              <p>Lade Schiffe...</p>
            </CardContent>
          </Card>
        ) : ships.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <p className="text-muted-foreground text-center">
                Noch keine Schiffe angelegt.
              </p>
            </CardContent>
          </Card>
        ) : (
          ships.map((ship: any) => (
            <Card key={ship.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Ship className="h-5 w-5" />
                      {ship.name}
                    </CardTitle>
                    <CardDescription>
                      Firma: {ship.user?.companyName} ({ship.user?.username})
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(ship)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(ship.id)}
                      disabled={deleteShipMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Länge:</span>
                    <div>{ship.length ? `${ship.length}m` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Breite:</span>
                    <div>{ship.width ? `${ship.width}m` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Einstieg 1:</span>
                    <div>{ship.entryHeight1 ? `${ship.entryHeight1}cm` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Einstieg 2:</span>
                    <div>{ship.entryHeight2 ? `${ship.entryHeight2}cm` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Sonnendeck:</span>
                    <div>{ship.sundeckEntryHeight ? `${ship.sundeckEntryHeight}cm` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Entf. v. Bug:</span>
                    <div>{ship.entryDistanceFromBow ? `${ship.entryDistanceFromBow}m` : "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Max. Passagiere:</span>
                    <div>{ship.maxPassengers || "—"}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}