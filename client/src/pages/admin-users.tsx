import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Users, Edit, Trash2, Shield, User } from "lucide-react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

interface UserFormData {
  username: string;
  email: string;
  companyName: string;
  phoneNumber: string;
  language: string;
  isAdmin: boolean;
}

export default function AdminUsersPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);

  const [formData, setFormData] = useState<UserFormData>({
    username: "",
    email: "",
    companyName: "",
    phoneNumber: "",
    language: "de",
    isAdmin: false,
  });

  // Lade alle Benutzer für Admin
  const { data: users = [], isLoading: usersLoading } = useQuery({
    queryKey: ["/api/admin/users"],
    enabled: user?.isAdmin,
  });

  const updateUserMutation = useMutation({
    mutationFn: async (userData: any) => {
      const res = await apiRequest("PUT", `/api/admin/users/${editingUser.id}`, userData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsDialogOpen(false);
      setEditingUser(null);
      resetForm();
      toast({
        title: "Benutzer aktualisiert",
        description: "Die Benutzerdaten wurden erfolgreich aktualisiert.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Aktualisieren des Benutzers",
        variant: "destructive",
      });
    },
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (userId: number) => {
      const res = await apiRequest("DELETE", `/api/admin/users/${userId}`);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      toast({
        title: "Benutzer gelöscht",
        description: "Der Benutzer wurde erfolgreich gelöscht.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message || "Fehler beim Löschen des Benutzers",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      username: "",
      email: "",
      companyName: "",
      phoneNumber: "",
      language: "de",
      isAdmin: false,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const userData = {
      username: formData.username,
      email: formData.email,
      companyName: formData.companyName,
      phoneNumber: formData.phoneNumber || null,
      language: formData.language,
      isAdmin: formData.isAdmin,
    };

    updateUserMutation.mutate(userData);
  };

  const handleEdit = (selectedUser: any) => {
    setEditingUser(selectedUser);
    setFormData({
      username: selectedUser.username || "",
      email: selectedUser.email || "",
      companyName: selectedUser.companyName || "",
      phoneNumber: selectedUser.phoneNumber || "",
      language: selectedUser.language || "de",
      isAdmin: selectedUser.isAdmin || false,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (userId: number, username: string) => {
    if (userId === user?.id) {
      toast({
        title: "Fehler",
        description: "Sie können sich nicht selbst löschen.",
        variant: "destructive",
      });
      return;
    }

    if (confirm(`Sind Sie sicher, dass Sie den Benutzer "${username}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.`)) {
      deleteUserMutation.mutate(userId);
    }
  };

  const getLanguageName = (lang: string) => {
    switch (lang) {
      case "de": return "Deutsch";
      case "en": return "English";
      default: return lang;
    }
  };

  if (!user?.isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Sie haben keine Berechtigung für diese Seite.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Benutzerverwaltung</h1>
          <p className="text-muted-foreground">Verwalten Sie alle Benutzer im System</p>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Benutzer bearbeiten: {editingUser?.username}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="username">Benutzername *</Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                  required
                  placeholder="Benutzername"
                />
              </div>
              <div>
                <Label htmlFor="email">E-Mail *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  required
                  placeholder="E-Mail-Adresse"
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="companyName">Firmenname *</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => setFormData(prev => ({ ...prev, companyName: e.target.value }))}
                  required
                  placeholder="Name der Firma"
                />
              </div>
              <div>
                <Label htmlFor="phoneNumber">Telefonnummer</Label>
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                  placeholder="Telefonnummer"
                />
              </div>
              <div>
                <Label htmlFor="language">Sprache</Label>
                <Select
                  value={formData.language}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sprache auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="de">Deutsch</SelectItem>
                    <SelectItem value="en">English</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2 flex items-center space-x-2">
                <Switch
                  id="isAdmin"
                  checked={formData.isAdmin}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isAdmin: checked }))}
                />
                <Label htmlFor="isAdmin">Administrator-Berechtigung</Label>
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Button type="submit" disabled={updateUserMutation.isPending}>
                Aktualisieren
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsDialogOpen(false)}
              >
                Abbrechen
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <div className="grid gap-4">
        {usersLoading ? (
          <Card>
            <CardContent className="p-6">
              <p>Lade Benutzer...</p>
            </CardContent>
          </Card>
        ) : users.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <p className="text-muted-foreground text-center">
                Keine Benutzer gefunden.
              </p>
            </CardContent>
          </Card>
        ) : (
          users.map((selectedUser: any) => (
            <Card key={selectedUser.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {selectedUser.isAdmin ? (
                        <Shield className="h-5 w-5 text-orange-500" />
                      ) : (
                        <User className="h-5 w-5" />
                      )}
                      {selectedUser.username}
                      {selectedUser.isAdmin && (
                        <Badge variant="secondary">Admin</Badge>
                      )}
                      {selectedUser.id === user?.id && (
                        <Badge variant="outline">Sie</Badge>
                      )}
                    </CardTitle>
                    <CardDescription>
                      {selectedUser.companyName}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(selectedUser)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(selectedUser.id, selectedUser.username)}
                      disabled={deleteUserMutation.isPending || selectedUser.id === user?.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium">E-Mail:</span>
                    <div>{selectedUser.email}</div>
                  </div>
                  <div>
                    <span className="font-medium">Telefon:</span>
                    <div>{selectedUser.phoneNumber || "—"}</div>
                  </div>
                  <div>
                    <span className="font-medium">Sprache:</span>
                    <div>{getLanguageName(selectedUser.language)}</div>
                  </div>
                  <div>
                    <span className="font-medium">Erstellt:</span>
                    <div>{new Date(selectedUser.createdAt).toLocaleDateString('de-DE')}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}