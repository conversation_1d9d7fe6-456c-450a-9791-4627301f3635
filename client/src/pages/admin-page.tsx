import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Settings, Clock, CheckCircle, XCircle, Calendar, Ship as ShipIcon, Users, Plus } from "lucide-react";

import { AdminSettings, BookingEditRequestWithDetails, Ship, User, InsertShip } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import AppLayout from "@/components/app-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogTrigger, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { formatDateTime } from "@/lib/date-utils";
import UsersManagement from "@/components/users-management";

export default function AdminPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("settings");
  const [cutoffDateTime, setCutoffDateTime] = useState("");
  const [startDateTime, setStartDateTime] = useState("");
  const [endDateTime, setEndDateTime] = useState("");
  const [adminEmail, setAdminEmail] = useState("");
  const [selectedRequest, setSelectedRequest] = useState<BookingEditRequestWithDetails | null>(null);
  const [adminNotes, setAdminNotes] = useState("");
  
  // Test email mutation
  const testEmailMutation = useMutation({
    mutationFn: async (testEmail: string) => {
      const res = await apiRequest("POST", "/api/admin/test-email", {
        testEmail
      });
      return await res.json();
    },
    onSuccess: (data) => {
      toast({
        title: data.success ? "Test erfolgreich" : "Test fehlgeschlagen",
        description: data.message,
        variant: data.success ? "default" : "destructive",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Test fehlgeschlagen",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  const [shipSearchTerm, setShipSearchTerm] = useState("");
  const [showCreateShipDialog, setShowCreateShipDialog] = useState(false);
  const [newShipData, setNewShipData] = useState({
    name: "",
    length: "",
    width: "",
    entryHeight1: "",
    entryHeight2: "",
    sundeckEntryHeight: "",
    entryDistanceFromBow: "",
    maxPassengers: "",
    userId: ""
  });

  // Fetch admin settings
  const { data: settings } = useQuery<AdminSettings>({
    queryKey: ["/api/admin/settings"],
  });

  // Fetch booking edit requests
  const { data: requests = [] } = useQuery<BookingEditRequestWithDetails[]>({
    queryKey: ["/api/admin/booking-requests"],
  });

  // Fetch all ships for admin
  const { data: allShips = [] } = useQuery<(Ship & { user: Omit<User, 'password'> })[]>({
    queryKey: ["/api/admin/ships"],
  });

  // Fetch all docks
  const { data: docks = [] } = useQuery({
    queryKey: ["/api/docks"],
  });

  // Fetch all users for admin
  const { data: allUsers = [] } = useQuery<Omit<User, 'password'>[]>({
    queryKey: ["/api/admin/users"],
  });

  // Update admin settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: { editingCutoffTime: string | null; editingStartTime: string | null; editingEndTime: string | null }) => {
      const res = await apiRequest("PUT", "/api/admin/settings", data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/settings"] });
      toast({
        title: "Einstellungen gespeichert",
        description: "Die Admin-Einstellungen wurden erfolgreich aktualisiert.",
      });
    },
    onError: () => {
      toast({
        title: "Fehler",
        description: "Die Einstellungen konnten nicht gespeichert werden.",
        variant: "destructive",
      });
    },
  });

  // Handle request mutation
  const handleRequestMutation = useMutation({
    mutationFn: async (data: { id: number; status: string; adminNotes?: string }) => {
      const res = await apiRequest("PUT", `/api/admin/booking-requests/${data.id}`, {
        status: data.status,
        adminNotes: data.adminNotes,
      });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/booking-requests"] });
      setSelectedRequest(null);
      setAdminNotes("");
      toast({
        title: "Anfrage bearbeitet",
        description: "Die Bearbeitungsanfrage wurde erfolgreich bearbeitet.",
      });
    },
    onError: () => {
      toast({
        title: "Fehler",
        description: "Die Anfrage konnte nicht bearbeitet werden.",
        variant: "destructive",
      });
    },
  });

  // Create ship mutation
  const createShipMutation = useMutation({
    mutationFn: async (data: { 
      name: string; 
      length?: number | null; 
      width?: number | null;
      entryHeight1?: number | null;
      entryHeight2?: number | null;
      sundeckEntryHeight?: number | null;
      entryDistanceFromBow?: number | null;
      maxPassengers?: number | null; 
      userId: number 
    }) => {
      const res = await apiRequest("POST", "/api/admin/ships", data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/ships"] });
      setShowCreateShipDialog(false);
      setNewShipData({ 
        name: "", 
        length: "", 
        width: "",
        entryHeight1: "",
        entryHeight2: "",
        sundeckEntryHeight: "",
        entryDistanceFromBow: "",
        maxPassengers: "", 
        userId: "" 
      });
      toast({
        title: "Schiff erstellt",
        description: "Das neue Schiff wurde erfolgreich erstellt.",
      });
    },
    onError: () => {
      toast({
        title: "Fehler",
        description: "Das Schiff konnte nicht erstellt werden.",
        variant: "destructive",
      });
    },
  });

  // Initialize datetime fields from settings
  useEffect(() => {
    if (settings) {
      if (settings.editingCutoffTime) {
        const dateTime = new Date(settings.editingCutoffTime).toISOString().slice(0, 16);
        setCutoffDateTime(dateTime);
      }
      if (settings.editingStartTime) {
        const dateTime = new Date(settings.editingStartTime).toISOString().slice(0, 16);
        setStartDateTime(dateTime);
      }
      if (settings.editingEndTime) {
        const dateTime = new Date(settings.editingEndTime).toISOString().slice(0, 16);
        setEndDateTime(dateTime);
      }
      if (settings.adminEmail) {
        setAdminEmail(settings.adminEmail);
      }
    }
  }, [settings]);

  const handleSaveSettings = () => {
    updateSettingsMutation.mutate({
      editingCutoffTime: cutoffDateTime || null,
      editingStartTime: startDateTime || null,
      editingEndTime: endDateTime || null,
      adminEmail: adminEmail || null,
    });
  };

  const handleApproveRequest = () => {
    if (selectedRequest) {
      handleRequestMutation.mutate({
        id: selectedRequest.id,
        status: "approved",
        adminNotes,
      });
    }
  };

  const handleRejectRequest = () => {
    if (selectedRequest) {
      handleRequestMutation.mutate({
        id: selectedRequest.id,
        status: "rejected",
        adminNotes,
      });
    }
  };

  const handleCreateShip = () => {
    const userId = parseInt(newShipData.userId);
    const length = newShipData.length ? parseInt(newShipData.length) : null;
    const width = newShipData.width ? parseInt(newShipData.width) : null;
    const entryHeight1 = newShipData.entryHeight1 ? parseInt(newShipData.entryHeight1) : null;
    const entryHeight2 = newShipData.entryHeight2 ? parseInt(newShipData.entryHeight2) : null;
    const sundeckEntryHeight = newShipData.sundeckEntryHeight ? parseInt(newShipData.sundeckEntryHeight) : null;
    const entryDistanceFromBow = newShipData.entryDistanceFromBow ? parseInt(newShipData.entryDistanceFromBow) : null;
    const maxPassengers = newShipData.maxPassengers ? parseInt(newShipData.maxPassengers) : null;

    if (!newShipData.name || !userId) {
      toast({
        title: "Fehler",
        description: "Schiffsname und Benutzer sind erforderlich.",
        variant: "destructive",
      });
      return;
    }

    createShipMutation.mutate({
      name: newShipData.name,
      length,
      width,
      entryHeight1,
      entryHeight2,
      sundeckEntryHeight,
      entryDistanceFromBow,
      maxPassengers,
      userId
    });
  };

  const pendingRequests = requests.filter(req => req.status === "pending");
  const processedRequests = requests.filter(req => req.status !== "pending");

  // Filter ships based on search term
  const filteredShips = allShips.filter(ship => 
    ship.name.toLowerCase().includes(shipSearchTerm.toLowerCase()) ||
    ship.user.companyName?.toLowerCase().includes(shipSearchTerm.toLowerCase()) ||
    ship.user.username.toLowerCase().includes(shipSearchTerm.toLowerCase())
  );

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Settings className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Admin-Bereich</h1>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <Button
            variant={activeTab === "settings" ? "default" : "ghost"}
            onClick={() => setActiveTab("settings")}
            className="flex-1"
          >
            <Settings className="h-4 w-4 mr-2" />
            Einstellungen
          </Button>
          <Button
            variant={activeTab === "requests" ? "default" : "ghost"}
            onClick={() => setActiveTab("requests")}
            className="flex-1"
          >
            <Clock className="h-4 w-4 mr-2" />
            Bearbeitungsanfragen ({pendingRequests.length})
          </Button>
          <Button
            variant={activeTab === "ships" ? "default" : "ghost"}
            onClick={() => setActiveTab("ships")}
            className="flex-1"
          >
            <ShipIcon className="h-4 w-4 mr-2" />
            Schiffsverwaltung ({allShips.length})
          </Button>
          <Button
            variant={activeTab === "users" ? "default" : "ghost"}
            onClick={() => setActiveTab("users")}
            className="flex-1"
          >
            <Users className="h-4 w-4 mr-2" />
            Benutzerverwaltung
          </Button>
        </div>

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Bearbeitungseinstellungen
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Settings Display */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Aktuelle Einstellungen</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Bearbeitungsstopp:</strong>
                    <p className="text-gray-600">
                      {settings?.editingCutoffTime 
                        ? formatDateTime(new Date(settings.editingCutoffTime))
                        : "Nicht gesetzt"
                      }
                    </p>
                  </div>
                  <div>
                    <strong>Admin E-Mail:</strong>
                    <p className="text-gray-600">
                      {settings?.adminEmail || "Nicht gesetzt"}
                    </p>
                  </div>
                  <div>
                    <strong>Sperrung Start:</strong>
                    <p className="text-gray-600">
                      {settings?.editingStartTime 
                        ? formatDateTime(new Date(settings.editingStartTime))
                        : "Nicht gesetzt"
                      }
                    </p>
                  </div>
                  <div>
                    <strong>Sperrung Ende:</strong>
                    <p className="text-gray-600">
                      {settings?.editingEndTime 
                        ? formatDateTime(new Date(settings.editingEndTime))
                        : "Nicht gesetzt"
                      }
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cutoff-time">Bearbeitungsstopp-Zeitpunkt (Legacy)</Label>
                <Input
                  id="cutoff-time"
                  type="datetime-local"
                  value={cutoffDateTime}
                  onChange={(e) => setCutoffDateTime(e.target.value)}
                />
                <p className="text-sm text-gray-500">
                  Nach diesem Zeitpunkt können Benutzer Buchungen nur noch über Anfragen bearbeiten.
                </p>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Zeitraum-basierte Sperrung</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-time">Start der Sperrung</Label>
                    <Input
                      id="start-time"
                      type="datetime-local"
                      value={startDateTime}
                      onChange={(e) => setStartDateTime(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="end-time">Ende der Sperrung</Label>
                    <Input
                      id="end-time"
                      type="datetime-local"
                      value={endDateTime}
                      onChange={(e) => setEndDateTime(e.target.value)}
                    />
                  </div>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Während diesem Zeitraum sind nur Änderungsanfragen möglich. Normale Bearbeitung ist gesperrt.
                </p>
              </div>

              <div className="border-t pt-4">
                <div className="space-y-2">
                  <Label htmlFor="admin-email">Administrator E-Mail</Label>
                  <Input
                    id="admin-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={adminEmail}
                    onChange={(e) => setAdminEmail(e.target.value)}
                  />
                  <p className="text-sm text-gray-500">
                    E-Mail-Adresse für Benachrichtigungen über Buchungsänderungsanfragen.
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleSaveSettings}
                  disabled={updateSettingsMutation.isPending}
                >
                  {updateSettingsMutation.isPending ? "Speichert..." : "Einstellungen speichern"}
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={() => {
                    if (!adminEmail) {
                      toast({
                        title: "E-Mail erforderlich",
                        description: "Bitte geben Sie zuerst eine Admin-E-Mail-Adresse ein.",
                        variant: "destructive",
                      });
                      return;
                    }
                    
                    testEmailMutation.mutate(adminEmail);
                  }}
                  disabled={testEmailMutation.isPending}
                >
                  {testEmailMutation.isPending ? "Sendet..." : "Test-E-Mail senden"}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Requests Tab */}
        {activeTab === "requests" && (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Offene Bearbeitungsanfragen
                  {pendingRequests.length > 0 && (
                    <Badge variant="destructive">{pendingRequests.length}</Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {pendingRequests.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    Keine offenen Bearbeitungsanfragen vorhanden.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {pendingRequests.map((request) => (
                      <div key={request.id} className="border rounded-lg p-4 space-y-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">
                              Buchung #{request.booking.id} - {request.booking.ship.name}
                            </h3>
                            <p className="text-sm text-gray-600">
                              Von: {request.user.companyName}
                            </p>
                            <p className="text-sm text-gray-500">
                              Angefragt am: {formatDateTime(request.createdAt)}
                            </p>
                          </div>
                          <Badge variant="outline">Wartend</Badge>
                        </div>

                        <div className="bg-gray-50 p-3 rounded">
                          <h4 className="font-medium text-sm mb-2">Gewünschte Änderungen:</h4>
                          <div className="text-sm space-y-1">
                            {(() => {
                              try {
                                const changes = JSON.parse(request.requestedChanges);
                                const originalBooking = request.booking;
                                return (
                                  <>
                                    {changes.dockId && changes.dockId !== originalBooking.dockId && (
                                      <div className="space-y-1">
                                        <p><strong>Ort:</strong></p>
                                        <p className="text-gray-600 ml-4">Alt: {originalBooking.dock.name}</p>
                                        <p className="text-blue-600 ml-4">Neu: {docks?.find(d => d.id === changes.dockId)?.name || changes.dockId}</p>
                                      </div>
                                    )}
                                    {changes.arrivalDate && new Date(changes.arrivalDate).getTime() !== new Date(originalBooking.arrivalDate).getTime() && (
                                      <div className="space-y-1">
                                        <p><strong>Ankunft:</strong></p>
                                        <p className="text-gray-600 ml-4">Alt: {formatDateTime(new Date(originalBooking.arrivalDate))}</p>
                                        <p className="text-blue-600 ml-4">Neu: {formatDateTime(new Date(changes.arrivalDate))}</p>
                                      </div>
                                    )}
                                    {changes.departureDate && new Date(changes.departureDate).getTime() !== new Date(originalBooking.departureDate).getTime() && (
                                      <div className="space-y-1">
                                        <p><strong>Abfahrt:</strong></p>
                                        <p className="text-gray-600 ml-4">Alt: {formatDateTime(new Date(originalBooking.departureDate))}</p>
                                        <p className="text-blue-600 ml-4">Neu: {formatDateTime(new Date(changes.departureDate))}</p>
                                      </div>
                                    )}
                                    {changes.row && changes.row !== originalBooking.row && (
                                      <div className="space-y-1">
                                        <p><strong>Reihe:</strong></p>
                                        <p className="text-gray-600 ml-4">Alt: {originalBooking.row || "Keine"}</p>
                                        <p className="text-blue-600 ml-4">Neu: {changes.row}</p>
                                      </div>
                                    )}
                                    {changes.notes && changes.notes !== originalBooking.notes && (
                                      <div className="space-y-1">
                                        <p><strong>Notizen:</strong></p>
                                        <p className="text-gray-600 ml-4">Alt: {originalBooking.notes || "Keine"}</p>
                                        <p className="text-blue-600 ml-4">Neu: {changes.notes}</p>
                                      </div>
                                    )}
                                  </>
                                );
                              } catch (error) {
                                console.error('Error parsing requested changes:', error);
                                return <p className="text-red-500">Fehler beim Laden der Änderungen</p>;
                              }
                            })()}
                          </div>
                          {request.reason && (
                            <div className="mt-2">
                              <strong className="text-sm">Begründung:</strong>
                              <p className="text-sm text-gray-600">{request.reason}</p>
                            </div>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => setSelectedRequest(request)}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Genehmigen
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogTitle>Anfrage genehmigen</DialogTitle>
                              <div className="space-y-4">
                                <p>Möchten Sie diese Bearbeitungsanfrage genehmigen?</p>
                                <div className="space-y-2">
                                  <Label htmlFor="admin-notes">Admin-Notizen (optional)</Label>
                                  <Textarea
                                    id="admin-notes"
                                    value={adminNotes}
                                    onChange={(e) => setAdminNotes(e.target.value)}
                                    placeholder="Interne Notizen zur Genehmigung..."
                                  />
                                </div>
                                <div className="flex gap-2">
                                  <Button onClick={handleApproveRequest}>
                                    Genehmigen
                                  </Button>
                                  <Button variant="outline" onClick={() => setSelectedRequest(null)}>
                                    Abbrechen
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>

                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => setSelectedRequest(request)}
                              >
                                <XCircle className="h-4 w-4 mr-2" />
                                Ablehnen
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogTitle>Anfrage ablehnen</DialogTitle>
                              <div className="space-y-4">
                                <p>Möchten Sie diese Bearbeitungsanfrage ablehnen?</p>
                                <div className="space-y-2">
                                  <Label htmlFor="admin-notes">Ablehnungsgrund</Label>
                                  <Textarea
                                    id="admin-notes"
                                    value={adminNotes}
                                    onChange={(e) => setAdminNotes(e.target.value)}
                                    placeholder="Grund für die Ablehnung..."
                                    required
                                  />
                                </div>
                                <div className="flex gap-2">
                                  <Button variant="destructive" onClick={handleRejectRequest}>
                                    Ablehnen
                                  </Button>
                                  <Button variant="outline" onClick={() => setSelectedRequest(null)}>
                                    Abbrechen
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Processed Requests */}
            {processedRequests.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Bearbeitete Anfragen</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {processedRequests.slice(0, 10).map((request) => (
                      <div key={request.id} className="border rounded p-3 text-sm">
                        <div className="flex justify-between items-center">
                          <span>
                            Buchung #{request.booking.id} - {request.booking.ship.name}
                          </span>
                          <Badge variant={request.status === "approved" ? "default" : "destructive"}>
                            {request.status === "approved" ? "Genehmigt" : "Abgelehnt"}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mt-1">
                          Von: {request.user.companyName} | 
                          Bearbeitet: {request.reviewedAt ? formatDateTime(request.reviewedAt) : "—"}
                        </p>
                        {request.adminNotes && (
                          <p className="text-gray-500 mt-1">
                            <strong>Admin-Notizen:</strong> {request.adminNotes}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Ships Tab */}
        {activeTab === "ships" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShipIcon className="h-5 w-5" />
                Schiffsverwaltung für alle Kunden
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search and Create */}
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Schiff, Firma oder Benutzername suchen..."
                  value={shipSearchTerm}
                  onChange={(e) => setShipSearchTerm(e.target.value)}
                  className="flex-1"
                />
                <Dialog open={showCreateShipDialog} onOpenChange={setShowCreateShipDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Neues Schiff
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogTitle>Neues Schiff erstellen</DialogTitle>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="ship-name">Schiffsname *</Label>
                        <Input
                          id="ship-name"
                          value={newShipData.name}
                          onChange={(e) => setNewShipData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Name des Schiffes..."
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="ship-user">Benutzer *</Label>
                        <select
                          id="ship-user"
                          value={newShipData.userId}
                          onChange={(e) => setNewShipData(prev => ({ ...prev, userId: e.target.value }))}
                          className="w-full p-2 border rounded-md"
                        >
                          <option value="">Benutzer auswählen...</option>
                          {allUsers.map((user) => (
                            <option key={user.id} value={user.id}>
                              {user.companyName || user.username} ({user.username})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="ship-length">Länge (m)</Label>
                          <Input
                            id="ship-length"
                            type="number"
                            value={newShipData.length}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, length: e.target.value }))}
                            placeholder="Länge in Metern..."
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="ship-width">Breite (m)</Label>
                          <Input
                            id="ship-width"
                            type="number"
                            value={newShipData.width}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, width: e.target.value }))}
                            placeholder="Breite in Metern..."
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="ship-entry1">Einstiegshöhe 1 (cm)</Label>
                          <Input
                            id="ship-entry1"
                            type="number"
                            value={newShipData.entryHeight1}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, entryHeight1: e.target.value }))}
                            placeholder="Höhe in cm..."
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="ship-entry2">Einstiegshöhe 2 (cm)</Label>
                          <Input
                            id="ship-entry2"
                            type="number"
                            value={newShipData.entryHeight2}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, entryHeight2: e.target.value }))}
                            placeholder="Höhe in cm..."
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="ship-sundeck">Einstiegshöhe Sonnendeck (cm)</Label>
                          <Input
                            id="ship-sundeck"
                            type="number"
                            value={newShipData.sundeckEntryHeight}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, sundeckEntryHeight: e.target.value }))}
                            placeholder="Höhe in cm..."
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="ship-distance">Entfernung Einstieg vom Bug (m)</Label>
                          <Input
                            id="ship-distance"
                            type="number"
                            value={newShipData.entryDistanceFromBow}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, entryDistanceFromBow: e.target.value }))}
                            placeholder="Entfernung in Metern..."
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="ship-passengers">Max. Passagiere</Label>
                          <Input
                            id="ship-passengers"
                            type="number"
                            value={newShipData.maxPassengers}
                            onChange={(e) => setNewShipData(prev => ({ ...prev, maxPassengers: e.target.value }))}
                            placeholder="Maximale Passagierzahl..."
                          />
                        </div>
                      </div>

                      <div className="flex gap-2 pt-4">
                        <Button 
                          onClick={handleCreateShip}
                          disabled={createShipMutation.isPending}
                        >
                          {createShipMutation.isPending ? "Erstellt..." : "Schiff erstellen"}
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => {
                            setShowCreateShipDialog(false);
                            setNewShipData({ 
                              name: "", 
                              length: "", 
                              width: "",
                              entryHeight1: "",
                              entryHeight2: "",
                              sundeckEntryHeight: "",
                              entryDistanceFromBow: "",
                              maxPassengers: "", 
                              userId: "" 
                            });
                          }}
                        >
                          Abbrechen
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* Ships List */}
              {filteredShips.length === 0 ? (
                <div className="text-center py-12">
                  <ShipIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Schiffe gefunden</h3>
                  <p className="text-gray-500">
                    {shipSearchTerm ? "Keine Schiffe entsprechen den Suchkriterien." : "Keine Schiffe registriert."}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredShips.map((ship) => (
                    <div key={ship.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{ship.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                            <span className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {ship.user.companyName || ship.user.username}
                            </span>
                            {ship.length && (
                              <span>Länge: {ship.length}m</span>
                            )}
                            {ship.maxPassengers && (
                              <span>Max. Passagiere: {ship.maxPassengers}</span>
                            )}
                          </div>
                        </div>
                        <div className="text-right text-sm text-gray-500">
                          <p>ID: {ship.id}</p>
                          <p>Benutzer-ID: {ship.userId}</p>
                        </div>
                      </div>
                      
                      {/* Contact Info */}
                      <div className="mt-3 pt-3 border-t">
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Kontaktinformationen:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Benutzername:</span> {ship.user.username}
                          </div>
                          {ship.user.email && (
                            <div>
                              <span className="font-medium">E-Mail:</span> {ship.user.email}
                            </div>
                          )}
                          {ship.user.phoneNumber && (
                            <div>
                              <span className="font-medium">Telefon:</span> {ship.user.phoneNumber}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Users Tab */}
        {activeTab === "users" && (
          <UsersManagement />
        )}
      </div>
    </AppLayout>
  );
}