import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Ship as ShipIcon, Anchor, BarChart, Calendar, Plus } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";

import { BookingWithDetails, Ship as ShipType } from "@shared/schema";
import { useAuth } from "@/hooks/useAuth";
import { formatDateTime } from "@/lib/date-utils";
import { queryClient } from "@/lib/queryClient";
import AppLayout from "@/components/app-layout";
import BookingForm from "@/components/booking-form";
import ShipForm from "@/components/ship-form";
import ConfirmDialog from "@/components/confirm-dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

export default function DashboardPage() {
  const { user } = useAuth();
  const [isBookingFormOpen, setIsBookingFormOpen] = useState(false);

  // Fetch bookings
  const { data: bookings = [] } = useQuery<BookingWithDetails[]>({
    queryKey: ["/api/bookings"],
  });

  // Fetch ships
  const { data: ships = [] } = useQuery<ShipType[]>({
    queryKey: ["/api/ships"],
  });

  // Fetch docks
  const { data: docks = [] } = useQuery({
    queryKey: ["/api/docks"],
  });

  // Calculate stats
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const currentTime = new Date().getTime();

  // Zeige alle Buchungen in verschiedenen Status an
  const activeBookings = bookings.filter(b => {
    // Zeige alle Buchungen, die nicht storniert sind
    // und entweder:
    // - zukünftig sind (Abfahrtsdatum liegt in der Zukunft)
    // - oder in den letzten 7 Tagen abgefahren sind (für kürzlich durchgeführte Buchungen)
    const departureTime = new Date(b.departureDate).getTime();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const sevenDaysAgoTime = sevenDaysAgo.getTime();
    
    // Buchung ist nicht storniert UND (Buchung ist in der Zukunft ODER Buchung ist nicht älter als 7 Tage)
    return b.status !== "cancelled" && (departureTime >= currentTime || departureTime >= sevenDaysAgoTime);
  });
  
  const arrivalsToday = bookings.filter(b => {
    const arrivalDate = new Date(b.arrivalDate);
    arrivalDate.setHours(0, 0, 0, 0);
    return arrivalDate.getTime() === today.getTime() && b.status !== "cancelled";
  });

  const docksUsed = Array.from(new Set(activeBookings.map(b => b.dockId))).length;

  // Format current date
  const currentDate = format(new Date(), "EEEE, dd. MMMM yyyy", { locale: de });

  // Get upcoming bookings (limited to 3)
  const upcomingBookings = [...activeBookings]
    .sort((a, b) => new Date(a.arrivalDate).getTime() - new Date(b.arrivalDate).getTime())
    .slice(0, 3);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Message */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold">Willkommen zurück, {user?.companyName}</h1>
            <p className="text-gray-500">{currentDate}</p>
          </div>
          <Dialog open={isBookingFormOpen} onOpenChange={setIsBookingFormOpen}>
            <DialogTrigger asChild>
              <Button className="mt-4 md:mt-0">
                <Plus className="mr-2 h-4 w-4" /> Neue Buchung
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogTitle>Neue Buchung erstellen</DialogTitle>
              <BookingForm onSuccess={() => setIsBookingFormOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-white">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-primary-100 text-primary">
                  <ShipIcon className="h-5 w-5" />
                </div>
                <div className="ml-4">
                  <h3 className="text-gray-500 text-sm">Aktive Buchungen</h3>
                  <p className="text-gray-800 font-semibold text-xl">
                    {activeBookings.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-green-100 text-green-600">
                  <Calendar className="h-5 w-5" />
                </div>
                <div className="ml-4">
                  <h3 className="text-gray-500 text-sm">Heutige Ankünfte</h3>
                  <p className="text-gray-800 font-semibold text-xl">
                    {arrivalsToday.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-blue-100 text-blue-600">
                  <Anchor className="h-5 w-5" />
                </div>
                <div className="ml-4">
                  <h3 className="text-gray-500 text-sm">Verfügbare Schiffe</h3>
                  <p className="text-gray-800 font-semibold text-xl">
                    {ships.length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="rounded-full p-2 bg-amber-100 text-amber-600">
                  <BarChart className="h-5 w-5" />
                </div>
                <div className="ml-4">
                  <h3 className="text-gray-500 text-sm">Anlegestellen genutzt</h3>
                  <p className="text-gray-800 font-semibold text-xl">
                    {docksUsed}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Bookings */}
        <div>
          <div>
            <Card className="bg-white">
              <div className="p-4 border-b border-gray-200">
                <h2 className="font-heading font-semibold text-lg text-gray-800">Kommende Buchungen</h2>
              </div>
              <CardContent className="p-4">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schiff</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ort</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ankunft</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Abfahrt</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Aktionen</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {upcomingBookings.length > 0 ? (
                        upcomingBookings.map((booking) => (
                          <tr key={booking.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                {booking.ship.name}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {booking.dock.name}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {formatDateTime(booking.arrivalDate)}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {formatDateTime(booking.departureDate)}
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <StatusBadge status={booking.status} />
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button variant="link" className="text-primary h-auto p-0 mr-3">
                                    Bearbeiten
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-3xl">
                                  <DialogTitle>Buchung bearbeiten</DialogTitle>
                                  <BookingForm bookingId={booking.id} />
                                </DialogContent>
                              </Dialog>
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button variant="link" className="text-destructive h-auto p-0">
                                    Löschen
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogTitle>Buchung löschen</DialogTitle>
                                  <div className="py-4">
                                    <p>Möchten Sie diese Buchung wirklich löschen?</p>
                                    <div className="flex justify-end gap-2 mt-4">
                                      <DialogClose asChild>
                                        <Button variant="outline">Abbrechen</Button>
                                      </DialogClose>

                                      <DialogClose asChild>
                                        <Button 
                                          variant="destructive"
                                          onClick={() => {
                                            // API Aufruf zum Löschen der Buchung
                                            fetch(`/api/bookings/${booking.id}`, {
                                              method: 'DELETE',
                                            }).then(() => {
                                              queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
                                            });
                                          }}
                                        >
                                          Löschen
                                        </Button>
                                      </DialogClose>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                            Keine kommenden Buchungen vorhanden
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                {bookings.length > 3 && (
                  <div className="mt-4 text-center">
                    <Button variant="link" asChild>
                      <a href="/bookings" className="text-primary">
                        Alle Buchungen anzeigen →
                      </a>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "confirmed":
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
          Bestätigt
        </Badge>
      );
    case "pending":
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          Anfrage
        </Badge>
      );
    case "cancelled":
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
          Storniert
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
          {status}
        </Badge>
      );
  }
}
