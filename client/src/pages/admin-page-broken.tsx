import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Settings, Clock, CheckCircle, XCircle, Calendar, Ship as ShipIcon, Users } from "lucide-react";

import { AdminSettings, BookingEditRequestWithDetails, Ship, User } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import AppLayout from "@/components/app-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogTrigger, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { formatDateTime } from "@/lib/date-utils";

export default function AdminPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("settings");
  const [cutoffDateTime, setCutoffDateTime] = useState("");
  const [selectedRequest, setSelectedRequest] = useState<BookingEditRequestWithDetails | null>(null);
  const [adminNotes, setAdminNotes] = useState("");
  const [shipSearchTerm, setShipSearchTerm] = useState("");

  // Fetch admin settings
  const { data: settings } = useQuery<AdminSettings>({
    queryKey: ["/api/admin/settings"],
  });

  // Fetch booking edit requests
  const { data: requests = [] } = useQuery<BookingEditRequestWithDetails[]>({
    queryKey: ["/api/admin/booking-requests"],
  });

  // Fetch all ships for admin
  const { data: allShips = [] } = useQuery<(Ship & { user: Omit<User, 'password'> })[]>({
    queryKey: ["/api/admin/ships"],
  });

  // Update admin settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: { editingCutoffTime: string | null }) => {
      const res = await apiRequest("PUT", "/api/admin/settings", data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/settings"] });
      toast({
        title: "Einstellungen gespeichert",
        description: "Die Admin-Einstellungen wurden erfolgreich aktualisiert.",
      });
    },
    onError: () => {
      toast({
        title: "Fehler",
        description: "Die Einstellungen konnten nicht gespeichert werden.",
        variant: "destructive",
      });
    },
  });

  // Handle booking edit request mutation
  const handleRequestMutation = useMutation({
    mutationFn: async (data: { id: number; status: string; adminNotes?: string }) => {
      const res = await apiRequest("PUT", `/api/admin/booking-requests/${data.id}`, {
        status: data.status,
        adminNotes: data.adminNotes,
      });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/booking-requests"] });
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      setSelectedRequest(null);
      setAdminNotes("");
      toast({
        title: "Anfrage bearbeitet",
        description: "Die Bearbeitungsanfrage wurde erfolgreich bearbeitet.",
      });
    },
    onError: () => {
      toast({
        title: "Fehler",
        description: "Die Anfrage konnte nicht bearbeitet werden.",
        variant: "destructive",
      });
    },
  });

  const handleSaveSettings = () => {
    updateSettingsMutation.mutate({ editingCutoffTime: cutoffDateTime || null });
  };

  const handleApproveRequest = () => {
    if (selectedRequest) {
      handleRequestMutation.mutate({
        id: selectedRequest.id,
        status: "approved",
        adminNotes,
      });
    }
  };

  const handleRejectRequest = () => {
    if (selectedRequest) {
      handleRequestMutation.mutate({
        id: selectedRequest.id,
        status: "rejected",
        adminNotes,
      });
    }
  };

  const pendingRequests = requests.filter(req => req.status === "pending");
  const processedRequests = requests.filter(req => req.status !== "pending");

  // Filter ships based on search term
  const filteredShips = allShips.filter(ship => 
    ship.name.toLowerCase().includes(shipSearchTerm.toLowerCase()) ||
    ship.user.companyName?.toLowerCase().includes(shipSearchTerm.toLowerCase()) ||
    ship.user.username.toLowerCase().includes(shipSearchTerm.toLowerCase())
  );

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Settings className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Admin-Bereich</h1>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          <Button
            variant={activeTab === "settings" ? "default" : "ghost"}
            onClick={() => setActiveTab("settings")}
            className="flex-1"
          >
            <Settings className="h-4 w-4 mr-2" />
            Einstellungen
          </Button>
          <Button
            variant={activeTab === "requests" ? "default" : "ghost"}
            onClick={() => setActiveTab("requests")}
            className="flex-1"
          >
            <Clock className="h-4 w-4 mr-2" />
            Bearbeitungsanfragen ({pendingRequests.length})
          </Button>
          <Button
            variant={activeTab === "ships" ? "default" : "ghost"}
            onClick={() => setActiveTab("ships")}
            className="flex-1"
          >
            <ShipIcon className="h-4 w-4 mr-2" />
            Schiffsverwaltung ({allShips.length})
          </Button>
        </div>

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Bearbeitungseinstellungen
              </CardTitle>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="cutoff-time">Bearbeitungsstopp-Zeitpunkt</Label>
              <Input
                id="cutoff-time"
                type="datetime-local"
                value={cutoffDateTime || (settings?.editingCutoffTime ? 
                  new Date(settings.editingCutoffTime).toISOString().slice(0, 16) : "")}
                onChange={(e) => setCutoffDateTime(e.target.value)}
                placeholder="Wählen Sie einen Zeitpunkt"
              />
              <p className="text-sm text-gray-500">
                Nach diesem Zeitpunkt können Benutzer Buchungen nur noch über Anfragen bearbeiten.
              </p>
            </div>
            <Button 
              onClick={handleSaveSettings}
              disabled={updateSettingsMutation.isPending}
            >
              {updateSettingsMutation.isPending ? "Speichert..." : "Einstellungen speichern"}
            </Button>
          </CardContent>
        </Card>
        )}

        {/* Requests Tab */}
        {activeTab === "requests" && (
        <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Offene Bearbeitungsanfragen
              {pendingRequests.length > 0 && (
                <Badge variant="destructive">{pendingRequests.length}</Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {pendingRequests.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                Keine offenen Bearbeitungsanfragen vorhanden.
              </p>
            ) : (
              <div className="space-y-4">
                {pendingRequests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">
                          Buchung #{request.booking.id} - {request.booking.ship.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Von: {request.user.companyName}
                        </p>
                        <p className="text-sm text-gray-500">
                          Angefragt am: {formatDateTime(request.createdAt)}
                        </p>
                      </div>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => {
                              setSelectedRequest(request);
                              setAdminNotes("");
                            }}
                          >
                            Bearbeiten
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogTitle>Bearbeitungsanfrage prüfen</DialogTitle>
                          {selectedRequest && (
                            <div className="space-y-4">
                              <div>
                                <h3 className="font-medium mb-2">Buchungsdetails:</h3>
                                <div className="bg-gray-50 p-3 rounded text-sm">
                                  <p><strong>Schiff:</strong> {selectedRequest.booking.ship.name}</p>
                                  <p><strong>Ort:</strong> {selectedRequest.booking.dock.name}</p>
                                  <p><strong>Ankunft:</strong> {formatDateTime(selectedRequest.booking.arrivalDate)}</p>
                                  <p><strong>Abfahrt:</strong> {formatDateTime(selectedRequest.booking.departureDate)}</p>
                                </div>
                              </div>
                              
                              <div>
                                <h3 className="font-medium mb-2">Gewünschte Änderungen:</h3>
                                <div className="bg-blue-50 p-3 rounded text-sm">
                                  <pre className="whitespace-pre-wrap">
                                    {JSON.stringify(JSON.parse(selectedRequest.requestedChanges), null, 2)}
                                  </pre>
                                </div>
                              </div>

                              <div>
                                <Label htmlFor="admin-notes">Admin-Notizen (optional)</Label>
                                <Textarea
                                  id="admin-notes"
                                  value={adminNotes}
                                  onChange={(e) => setAdminNotes(e.target.value)}
                                  placeholder="Notizen zur Entscheidung..."
                                />
                              </div>

                              <div className="flex gap-2 justify-end">
                                <Button
                                  variant="outline"
                                  onClick={handleRejectRequest}
                                  disabled={handleRequestMutation.isPending}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Ablehnen
                                </Button>
                                <Button
                                  onClick={handleApproveRequest}
                                  disabled={handleRequestMutation.isPending}
                                  className="bg-green-600 hover:bg-green-700"
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Genehmigen
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Processed Requests */}
        {processedRequests.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Bearbeitete Anfragen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {processedRequests.slice(0, 10).map((request) => (
                  <div key={request.id} className="border rounded-lg p-4 flex justify-between items-center">
                    <div>
                      <h3 className="font-medium">
                        Buchung #{request.booking.id} - {request.booking.ship.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        Von: {request.user.companyName}
                      </p>
                      <p className="text-sm text-gray-500">
                        Bearbeitet am: {request.reviewedAt ? formatDateTime(request.reviewedAt) : "—"}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge 
                        variant={request.status === "approved" ? "default" : "destructive"}
                        className={request.status === "approved" ? "bg-green-100 text-green-800" : ""}
                      >
                        {request.status === "approved" ? "Genehmigt" : "Abgelehnt"}
                      </Badge>
                      {request.adminNotes && (
                        <p className="text-sm text-gray-500 mt-1">
                          {request.adminNotes}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        )}

        {/* Ships Tab */}
        {activeTab === "ships" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShipIcon className="h-5 w-5" />
                Schiffsverwaltung für alle Kunden
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search */}
              <div className="flex items-center gap-2">
                <Input
                  placeholder="Schiff, Firma oder Benutzername suchen..."
                  value={shipSearchTerm}
                  onChange={(e) => setShipSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>

              {/* Ships List */}
              {filteredShips.length === 0 ? (
                <div className="text-center py-12">
                  <ShipIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Schiffe gefunden</h3>
                  <p className="text-gray-500">
                    {shipSearchTerm ? "Keine Schiffe entsprechen den Suchkriterien." : "Keine Schiffe registriert."}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredShips.map((ship) => (
                    <div key={ship.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{ship.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                            <span className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {ship.user.companyName || ship.user.username}
                            </span>
                            {ship.length && (
                              <span>Länge: {ship.length}m</span>
                            )}
                            {ship.maxPassengers && (
                              <span>Max. Passagiere: {ship.maxPassengers}</span>
                            )}
                          </div>
                        </div>
                        <div className="text-right text-sm text-gray-500">
                          <p>ID: {ship.id}</p>
                          <p>Benutzer-ID: {ship.userId}</p>
                        </div>
                      </div>
                      
                      {/* Contact Info */}
                      <div className="mt-3 pt-3 border-t">
                        <h4 className="font-medium text-sm text-gray-700 mb-2">Kontaktinformationen:</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Benutzername:</span> {ship.user.username}
                          </div>
                          {ship.user.email && (
                            <div>
                              <span className="font-medium">E-Mail:</span> {ship.user.email}
                            </div>
                          )}
                          {ship.user.phoneNumber && (
                            <div>
                              <span className="font-medium">Telefon:</span> {ship.user.phoneNumber}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}