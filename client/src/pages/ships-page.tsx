import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Search, Ship as ShipIcon } from "lucide-react";

import { Ship } from "@shared/schema";
import AppLayout from "@/components/app-layout";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";

export default function ShipsPage() {
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch ships
  const { 
    data: ships = [], 
    isLoading
  } = useQuery<Ship[]>({
    queryKey: ["/api/ships"],
  });

  // Filter ships based on search term
  const filteredShips = ships.filter(ship => 
    ship.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-2xl font-bold"><PERSON><PERSON></h1>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Suche nach Schiffsnamen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Ships Grid */}
        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : filteredShips.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredShips.map((ship) => (
              <Card key={ship.id} className="overflow-hidden">
                <div className="p-4 bg-primary text-white flex justify-between items-center">
                  <h3 className="font-semibold">{ship.name}</h3>
                  <ShipIcon className="h-5 w-5" />
                </div>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Länge</p>
                        <p className="font-medium">{ship.length || "—"} m</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Max. Passagiere</p>
                        <p className="font-medium">{ship.maxPassengers || "—"}</p>
                      </div>
                    </div>
                    

                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12 bg-white rounded-lg shadow mt-6">
            <ShipIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Schiffe gefunden</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              Sie haben noch keine Schiffe hinzugefügt oder es wurden keine Schiffe gefunden, die Ihren Suchkriterien entsprechen.
            </p>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
