import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ship, Calendar, Users, Shield } from "lucide-react";

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center mb-6">
            <Ship className="h-16 w-16 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Schiffsbuchungssystem
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Verwalten Sie Ihre Schiffsanläufe effizient und unkompliziert. 
            Buchen Sie Liegeplätze, verwalten Sie Ihre Flotte und behalten Sie den Überblick.
          </p>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <Calendar className="h-8 w-8 text-blue-600 mb-2" />
              <CardTitle>Einfache Buchungen</CardTitle>
              <CardDescription>
                Buchen Sie Liegeplätze schnell und unkompliziert für Ihre gesamte Flotte
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <Users className="h-8 w-8 text-green-600 mb-2" />
              <CardTitle>Flottenverwaltung</CardTitle>
              <CardDescription>
                Verwalten Sie alle Ihre Schiffe zentral mit detaillierten Informationen
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <Shield className="h-8 w-8 text-purple-600 mb-2" />
              <CardTitle>Sichere Anmeldung</CardTitle>
              <CardDescription>
                Sichere Authentifizierung über Replit mit modernsten Sicherheitsstandards
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle>Jetzt anmelden</CardTitle>
              <CardDescription>
                Melden Sie sich mit Ihrem Replit-Konto an, um das Schiffsbuchungssystem zu nutzen
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                size="lg" 
                className="w-full"
                onClick={() => window.location.href = '/api/login'}
              >
                Mit Replit anmelden
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="mt-16 text-center text-gray-500">
          <p>&copy; 2025 Schiffsbuchungssystem. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    </div>
  );
}