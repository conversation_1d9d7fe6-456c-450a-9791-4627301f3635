import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Ship, Mail, Lock, User, Building2, Phone, Globe } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useAuth } from "@/hooks/useAuth";
import { useLocation } from "wouter";
import { useLanguage } from "@/contexts/LanguageContext";

export default function AuthPage() {
  const { user, isLoading } = useAuth();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const { t, setLanguage, language } = useLanguage();

  // Redirect if already logged in
  if (!isLoading && user) {
    setLocation("/");
    return null;
  }

  const [loginData, setLoginData] = useState({
    email: "",
    password: "",
  });

  const [registerData, setRegisterData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    companyName: "",
    phoneNumber: "",
    language: language,
  });

  const loginMutation = useMutation({
    mutationFn: async (credentials: { email: string; password: string }) => {
      const res = await apiRequest("POST", "/api/login", credentials);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });
      setLocation("/");
      toast({
        title: language === 'de' ? "Anmeldung erfolgreich" : "Login successful",
        description: language === 'de' ? "Willkommen zurück!" : "Welcome back!",
      });
    },
    onError: (error: Error) => {
      toast({
        title: language === 'de' ? "Anmeldung fehlgeschlagen" : "Login failed",
        description: error.message || (language === 'de' ? "Ungültige E-Mail oder Passwort" : "Invalid email or password"),
        variant: "destructive",
      });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData: Omit<typeof registerData, 'confirmPassword'>) => {
      const res = await apiRequest("POST", "/api/register", userData);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user"] });
      setLocation("/");
      toast({
        title: language === 'de' ? "Registrierung erfolgreich" : "Registration successful",
        description: language === 'de' ? "Ihr Konto wurde erstellt!" : "Your account has been created!",
      });
    },
    onError: (error: Error) => {
      toast({
        title: language === 'de' ? "Registrierung fehlgeschlagen" : "Registration failed",
        description: error.message || (language === 'de' ? "Ein Fehler ist aufgetreten" : "An error occurred"),
        variant: "destructive",
      });
    },
  });

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate(loginData);
  };

  const handleRegister = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Passwort-Bestätigung prüfen
    if (registerData.password !== registerData.confirmPassword) {
      toast({
        title: "Fehler",
        description: "Die Passwörter stimmen nicht überein",
        variant: "destructive",
      });
      return;
    }
    
    // confirmPassword aus den Daten entfernen bevor sie an den Server gesendet werden
    const { confirmPassword, ...dataToSend } = registerData;
    registerMutation.mutate(dataToSend);
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl grid lg:grid-cols-2 gap-8 items-center">
        {/* Hero Section */}
        <div className="text-center lg:text-left">
          <div className="flex justify-center lg:justify-start items-center mb-6">
            <Ship className="h-16 w-16 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {language === 'de' ? 'Schiffsbuchungssystem' : 'Ship Booking System'}
          </h1>
          <p className="text-xl text-gray-600 mb-6">
            {language === 'de' 
              ? 'Verwalten Sie Ihre Schiffsanläufe effizient und unkompliziert. Buchen Sie Liegeplätze, verwalten Sie Ihre Flotte und behalten Sie den Überblick.'
              : 'Manage your ship arrivals efficiently and easily. Book berths, manage your fleet and keep track of everything.'
            }
          </p>
          <div className="space-y-4 text-gray-700">
            <div className="flex items-center justify-center lg:justify-start">
              <Ship className="h-5 w-5 text-blue-600 mr-3" />
              <span>{language === 'de' ? 'Einfache Buchungsverwaltung' : 'Simple booking management'}</span>
            </div>
            <div className="flex items-center justify-center lg:justify-start">
              <User className="h-5 w-5 text-green-600 mr-3" />
              <span>{language === 'de' ? 'Flottenverwaltung' : 'Fleet management'}</span>
            </div>
            <div className="flex items-center justify-center lg:justify-start">
              <Building2 className="h-5 w-5 text-purple-600 mr-3" />
              <span>{language === 'de' ? 'Mehrsprachige Unterstützung' : 'Multilingual support'}</span>
            </div>
          </div>
        </div>

        {/* Auth Forms */}
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{language === 'de' ? 'Anmeldung' : 'Authentication'}</CardTitle>
                <CardDescription>
                  {language === 'de' 
                    ? 'Melden Sie sich an oder erstellen Sie ein neues Konto'
                    : 'Sign in or create a new account'
                  }
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-gray-400" />
                <Select value={language} onValueChange={(value) => setLanguage(value as 'de' | 'en')}>
                  <SelectTrigger className="w-16">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="de">DE</SelectItem>
                    <SelectItem value="en">EN</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="login" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="login">{language === 'de' ? 'Anmelden' : 'Sign In'}</TabsTrigger>
                <TabsTrigger value="register">{language === 'de' ? 'Registrieren' : 'Register'}</TabsTrigger>
              </TabsList>

              <TabsContent value="login" className="space-y-4">
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-email">{language === 'de' ? 'E-Mail' : 'Email'}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="login-email"
                        type="email"
                        placeholder={language === 'de' ? '<EMAIL>' : '<EMAIL>'}
                        className="pl-10"
                        value={loginData.email}
                        onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="login-password">{language === 'de' ? 'Passwort' : 'Password'}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="login-password"
                        type="password"
                        placeholder={language === 'de' ? 'Ihr Passwort' : 'Your password'}
                        className="pl-10"
                        value={loginData.password}
                        onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={loginMutation.isPending}
                  >
                    {loginMutation.isPending 
                      ? (language === 'de' ? 'Anmelden...' : 'Signing in...') 
                      : (language === 'de' ? 'Anmelden' : 'Sign In')
                    }
                  </Button>
                </form>


              </TabsContent>

              <TabsContent value="register" className="space-y-4">
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="register-username">{language === 'de' ? 'Benutzername' : 'Username'}</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-username"
                        type="text"
                        placeholder={language === 'de' ? 'Benutzername' : 'Username'}
                        className="pl-10"
                        value={registerData.username}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, username: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-email">{language === 'de' ? 'E-Mail' : 'Email'}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-email"
                        type="email"
                        placeholder={language === 'de' ? '<EMAIL>' : '<EMAIL>'}
                        className="pl-10"
                        value={registerData.email}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, email: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-password">{language === 'de' ? 'Passwort' : 'Password'}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-password"
                        type="password"
                        placeholder={language === 'de' ? 'Sicheres Passwort' : 'Secure password'}
                        className="pl-10"
                        value={registerData.password}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, password: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-confirm-password">{language === 'de' ? 'Passwort bestätigen' : 'Confirm password'}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-confirm-password"
                        type="password"
                        placeholder={language === 'de' ? 'Passwort wiederholen' : 'Repeat password'}
                        className="pl-10"
                        value={registerData.confirmPassword}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-company">{language === 'de' ? 'Firmenname' : 'Company name'}</Label>
                    <div className="relative">
                      <Building2 className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-company"
                        type="text"
                        placeholder={language === 'de' ? 'Ihre Firma' : 'Your company'}
                        className="pl-10"
                        value={registerData.companyName}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, companyName: e.target.value }))}
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-phone">{language === 'de' ? 'Telefonnummer (optional)' : 'Phone number (optional)'}</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="register-phone"
                        type="tel"
                        placeholder={language === 'de' ? '+49 **********' : '****** 456789'}
                        className="pl-10"
                        value={registerData.phoneNumber}
                        onChange={(e) => setRegisterData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="register-language">{language === 'de' ? 'Bevorzugte Sprache' : 'Preferred language'}</Label>
                    <div className="relative">
                      <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Select 
                        value={registerData.language} 
                        onValueChange={(value) => setRegisterData(prev => ({ ...prev, language: value as 'de' | 'en' }))}
                      >
                        <SelectTrigger className="pl-10">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="de">{language === 'de' ? 'Deutsch' : 'German'}</SelectItem>
                          <SelectItem value="en">{language === 'de' ? 'Englisch' : 'English'}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={registerMutation.isPending}
                  >
                    {registerMutation.isPending 
                      ? (language === 'de' ? 'Registrieren...' : 'Registering...') 
                      : (language === 'de' ? 'Registrieren' : 'Register')
                    }
                  </Button>
                </form>


              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}