import { createContext, useContext, ReactNode, useEffect, useState } from "react";
import { useAuth } from "./use-auth";

// Definieren aller verfügbaren Übersetzungen
type Translations = {
  [key: string]: {
    [key: string]: string;
  };
};

const translations: Translations = {
  // Deutsche Übersetzungen
  de: {
    // Auth Page
    "auth.title": "Willkommen im Hafenverwaltungssystem",
    "auth.description": "Verwalten Sie Ihre Schiffsanlegestellen effizient",
    "auth.login": "Anmelden",
    "auth.register": "Registrieren",
    "auth.username": "Benutzername",
    "auth.password": "Passwort",
    "auth.confirmPassword": "Passwort bestätigen",
    "auth.companyName": "Unternehmensnamen",
    "auth.email": "E-Mail",
    "auth.phoneNumber": "Telefonnummer",
    "auth.language": "Sprache",
    "auth.german": "Deutsch",
    "auth.english": "Englisch",
    "auth.loginBtn": "Anmelden",
    "auth.registerBtn": "Konto erstellen",
    "auth.alreadyAccount": "Bereits ein Konto?",
    "auth.noAccount": "Noch kein Konto?",
    "auth.passwordsDontMatch": "Passwörter stimmen nicht überein",
    
    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.bookings": "Buchungen",
    "nav.ships": "Schiffe",
    "nav.profile": "Profil",
    "nav.logout": "Abmelden",
    
    // Dashboard
    "dashboard.title": "Dashboard",
    "dashboard.newBooking": "Neue Buchung",
    "dashboard.activeBookings": "Aktive Buchungen",
    "dashboard.arrivalsToday": "Heutige Ankünfte",
    "dashboard.availableShips": "Verfügbare Schiffe",
    "dashboard.docksUsed": "Anlegestellen genutzt",
    "dashboard.upcomingBookings": "Kommende Buchungen",
    "dashboard.quickActions": "Schnellaktionen",
    "dashboard.newBookingBtn": "Neue Buchung erstellen",
    "dashboard.checkAvailability": "Verfügbarkeit prüfen",
    "dashboard.addNewShip": "Neues Schiff hinzufügen",
    "dashboard.calendar": "Kalender",
    "dashboard.allBookings": "Alle Buchungen anzeigen",
    "dashboard.noUpcomingBookings": "Keine kommenden Buchungen vorhanden",
    "dashboard.calendarOverview": "Kalenderübersicht wird hier angezeigt",
    
    // Bookings
    "bookings.title": "Buchungen verwalten",
    "bookings.new": "Neue Buchung",
    "bookings.ship": "Schiff",
    "bookings.dock": "Ort",
    "bookings.arrival": "Ankunft",
    "bookings.departure": "Abfahrt",
    "bookings.status": "Status",
    "bookings.actions": "Aktionen",
    "bookings.edit": "Bearbeiten",
    "bookings.delete": "Löschen",
    "bookings.noBookings": "Keine Buchungen gefunden",
    "bookings.editBooking": "Buchung bearbeiten",
    "bookings.newBooking": "Neue Buchung erstellen",
    "bookings.deleteBooking": "Buchung löschen",
    "bookings.deleteConfirm": "Möchten Sie diese Buchung wirklich löschen?",
    "bookings.cancel": "Abbrechen",
    "bookings.confirm": "Bestätigen",
    "bookings.selectShip": "Schiff auswählen",
    "bookings.selectDock": "Anlegestelle auswählen",
    "bookings.arrivalDate": "Ankunftsdatum",
    "bookings.arrivalTime": "Ankunftszeit",
    "bookings.departureDate": "Abreisedatum",
    "bookings.departureTime": "Abreisezeit",
    "bookings.notes": "Anmerkungen",
    "bookings.saveBooking": "Buchung speichern",
    "bookings.status.confirmed": "Bestätigt",
    "bookings.status.pending": "Anfrage",
    "bookings.status.cancelled": "Storniert",
    
    // Ships
    "ships.title": "Schiffe verwalten",
    "ships.new": "Neues Schiff",
    "ships.name": "Name",
    "ships.length": "Länge",
    "ships.maxPassengers": "Max. Passagiere",
    "ships.actions": "Aktionen",
    "ships.edit": "Bearbeiten",
    "ships.delete": "Löschen",
    "ships.noShips": "Keine Schiffe gefunden",
    "ships.addShip": "Schiff hinzufügen",
    "ships.editShip": "Schiff bearbeiten",
    "ships.deleteShip": "Schiff löschen",
    "ships.deleteConfirm": "Möchten Sie dieses Schiff wirklich löschen?",
    "ships.meters": "Meter",
    "ships.saveShip": "Schiff speichern",
    
    // Profile
    "profile.title": "Profil",
    "profile.personalInfo": "Persönliche Informationen",
    "profile.username": "Benutzername",
    "profile.companyName": "Unternehmensnamen",
    "profile.email": "E-Mail",
    "profile.phoneNumber": "Telefonnummer",
    "profile.language": "Sprache",
    "profile.german": "Deutsch",
    "profile.english": "Englisch",
    "profile.updateProfile": "Profil aktualisieren",
    "profile.updateSuccess": "Profil erfolgreich aktualisiert",
    
    // Common
    "common.loading": "Laden...",
    "common.error": "Fehler",
    "common.success": "Erfolg",
    "common.back": "Zurück",
    "common.save": "Speichern",
    "common.cancel": "Abbrechen",
    "common.delete": "Löschen",
    "common.edit": "Bearbeiten",
    "common.create": "Erstellen",
    "common.required": "Pflichtfeld",
    "common.optional": "Optional"
  },
  
  // Englische Übersetzungen
  en: {
    // Auth Page
    "auth.title": "Welcome to the Harbor Management System",
    "auth.description": "Manage your ship docking locations efficiently",
    "auth.login": "Login",
    "auth.register": "Register",
    "auth.username": "Username",
    "auth.password": "Password",
    "auth.confirmPassword": "Confirm Password",
    "auth.companyName": "Company Name",
    "auth.email": "Email",
    "auth.phoneNumber": "Phone Number",
    "auth.language": "Language",
    "auth.german": "German",
    "auth.english": "English",
    "auth.loginBtn": "Sign In",
    "auth.registerBtn": "Create Account",
    "auth.alreadyAccount": "Already have an account?",
    "auth.noAccount": "Don't have an account?",
    "auth.passwordsDontMatch": "Passwords don't match",
    
    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.bookings": "Bookings",
    "nav.ships": "Ships",
    "nav.profile": "Profile",
    "nav.logout": "Logout",
    
    // Dashboard
    "dashboard.title": "Dashboard",
    "dashboard.newBooking": "New Booking",
    "dashboard.activeBookings": "Active Bookings",
    "dashboard.arrivalsToday": "Today's Arrivals",
    "dashboard.availableShips": "Available Ships",
    "dashboard.docksUsed": "Docks Used",
    "dashboard.upcomingBookings": "Upcoming Bookings",
    "dashboard.quickActions": "Quick Actions",
    "dashboard.newBookingBtn": "Create New Booking",
    "dashboard.checkAvailability": "Check Availability",
    "dashboard.addNewShip": "Add New Ship",
    "dashboard.calendar": "Calendar",
    "dashboard.allBookings": "View All Bookings",
    "dashboard.noUpcomingBookings": "No upcoming bookings available",
    "dashboard.calendarOverview": "Calendar overview will be displayed here",
    
    // Bookings
    "bookings.title": "Manage Bookings",
    "bookings.new": "New Booking",
    "bookings.ship": "Ship",
    "bookings.dock": "Dock",
    "bookings.arrival": "Arrival",
    "bookings.departure": "Departure",
    "bookings.status": "Status",
    "bookings.actions": "Actions",
    "bookings.edit": "Edit",
    "bookings.delete": "Delete",
    "bookings.noBookings": "No bookings found",
    "bookings.editBooking": "Edit Booking",
    "bookings.newBooking": "Create New Booking",
    "bookings.deleteBooking": "Delete Booking",
    "bookings.deleteConfirm": "Are you sure you want to delete this booking?",
    "bookings.cancel": "Cancel",
    "bookings.confirm": "Confirm",
    "bookings.selectShip": "Select Ship",
    "bookings.selectDock": "Select Dock",
    "bookings.arrivalDate": "Arrival Date",
    "bookings.arrivalTime": "Arrival Time",
    "bookings.departureDate": "Departure Date",
    "bookings.departureTime": "Departure Time",
    "bookings.notes": "Notes",
    "bookings.saveBooking": "Save Booking",
    "bookings.status.confirmed": "Confirmed",
    "bookings.status.pending": "Pending",
    "bookings.status.cancelled": "Cancelled",
    
    // Ships
    "ships.title": "Manage Ships",
    "ships.new": "New Ship",
    "ships.name": "Name",
    "ships.length": "Length",
    "ships.maxPassengers": "Max. Passengers",
    "ships.actions": "Actions",
    "ships.edit": "Edit",
    "ships.delete": "Delete",
    "ships.noShips": "No ships found",
    "ships.addShip": "Add Ship",
    "ships.editShip": "Edit Ship",
    "ships.deleteShip": "Delete Ship",
    "ships.deleteConfirm": "Are you sure you want to delete this ship?",
    "ships.meters": "meters",
    "ships.saveShip": "Save Ship",
    
    // Profile
    "profile.title": "Profile",
    "profile.personalInfo": "Personal Information",
    "profile.username": "Username",
    "profile.companyName": "Company Name",
    "profile.email": "Email",
    "profile.phoneNumber": "Phone Number",
    "profile.language": "Language",
    "profile.german": "German",
    "profile.english": "English",
    "profile.updateProfile": "Update Profile",
    "profile.updateSuccess": "Profile updated successfully",
    
    // Common
    "common.loading": "Loading...",
    "common.error": "Error",
    "common.success": "Success",
    "common.back": "Back",
    "common.save": "Save",
    "common.cancel": "Cancel",
    "common.delete": "Delete",
    "common.edit": "Edit",
    "common.create": "Create",
    "common.required": "Required",
    "common.optional": "Optional"
  }
};

// Context für die Sprachfunktionalität
type LanguageContextType = {
  language: string;
  t: (key: string) => string;
  changeLanguage: (lang: string) => void;
};

const LanguageContext = createContext<LanguageContextType | null>(null);

// Hook zum Zugriff auf die Sprachfunktionalität
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}

// Provider für die Sprachfunktionalität
export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState("de"); // Standardsprache: Deutsch

  // Funktion zum Übersetzen von Schlüsseln
  const t = (key: string): string => {
    if (!translations[language] || !translations[language][key]) {
      // Fallback, wenn die Übersetzung nicht gefunden wird
      return translations["de"][key] || key;
    }
    return translations[language][key];
  };

  // Funktion zum Ändern der Sprache
  const changeLanguage = (lang: string) => {
    if (lang === "de" || lang === "en") {
      setLanguage(lang);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, t, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}