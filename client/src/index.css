@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 210 40% 98%;
  --foreground: 222 47% 11%;
  --muted: 210 40% 93%;
  --muted-foreground: 215 16% 47%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 47% 11%;
  --card: 0 0% 100%;
  --card-foreground: 222 47% 11%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --primary: 210 83% 42%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 93%;
  --secondary-foreground: 222 47% 11%;
  --accent: 210 40% 93%;
  --accent-foreground: 222 47% 11%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --ring: 210 83% 42%;
  --radius: 0.5rem;

  /* Chart Colors */
  --chart-1: 210 83% 42%;
  --chart-2: 210 83% 62%;
  --chart-3: 180 83% 42%;
  --chart-4: 150 83% 42%;
  --chart-5: 120 83% 42%;

  /* Sidebar Colors */
  --sidebar-background: 210 83% 22%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 210 83% 15%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 210 83% 32%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 210 83% 32%;
  --sidebar-ring: 210 83% 42%;
}

.dark {
  --background: 222 47% 11%;
  --foreground: 210 40% 98%;
  --muted: 217 33% 17%;
  --muted-foreground: 215 20% 65%;
  --popover: 222 47% 11%;
  --popover-foreground: 210 40% 98%;
  --card: 222 47% 11%;
  --card-foreground: 210 40% 98%;
  --border: 217 33% 17%;
  --input: 217 33% 17%;
  --primary: 210 83% 42%;
  --primary-foreground: 0 0% 100%;
  --secondary: 217 33% 17%;
  --secondary-foreground: 210 40% 98%;
  --accent: 217 33% 17%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 210 83% 42%;
  --radius: 0.5rem;

  /* Sidebar Colors */
  --sidebar-background: 210 83% 15%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 210 83% 10%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 210 83% 25%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 210 83% 25%;
  --sidebar-ring: 210 83% 42%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Open Sans', ui-sans-serif, system-ui, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', ui-sans-serif, system-ui, sans-serif;
    @apply font-semibold;
  }
}

@layer utilities {
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-white hover:bg-sidebar-accent transition-colors duration-200;
  }
  
  .sidebar-link.active {
    @apply bg-sidebar-primary text-white font-semibold;
  }
  
  .sidebar-icon {
    @apply mr-3 h-5 w-5;
  }
}
