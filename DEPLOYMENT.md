# Ship Docking Management System - Deployment Guide

## Overview
This is a full-stack web application for managing ship docking reservations with admin controls and multilingual support (German/English).

## Tech Stack
- **Frontend**: React with TypeScript, Vite, TailwindCSS, shadcn/ui
- **Backend**: Node.js with Express, TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Passport.js with local strategy
- **Email**: Nodemailer with Gmail SMTP

## Prerequisites
- Node.js 18+ 
- PostgreSQL database
- Gmail account for email notifications (optional)

## Environment Variables

Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL=postgresql://username:password@host:port/database_name
PGHOST=your_postgres_host
PGPORT=5432
PGDATABASE=your_database_name
PGUSER=your_postgres_user
PGPASSWORD=your_postgres_password

# Session Security
SESSION_SECRET=your_secure_session_secret_here

# Email Configuration (Optional)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_gmail_app_password

# Production
NODE_ENV=production
PORT=5000
```

## Database Setup

1. Create a PostgreSQL database
2. The application will automatically create tables on first run using Drizzle ORM
3. Default dock locations are pre-populated with Austrian Danube locations

## Installation & Build

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Push database schema (first time only)
npm run db:push
```

## Running in Production

```bash
# Start the production server
npm start
```

The application serves both frontend and backend on the same port (default: 5000).

## File Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/         # Route pages
│   │   ├── hooks/         # Custom hooks
│   │   ├── lib/           # Utilities
│   │   └── contexts/      # React contexts
│   └── dist/              # Built frontend (generated)
├── server/                # Express backend
│   ├── auth.ts           # Authentication setup
│   ├── routes.ts         # API endpoints
│   ├── storage.ts        # Database operations
│   ├── email.ts          # Email notifications
│   └── index.ts          # Server entry point
├── shared/               # Shared types and schemas
│   └── schema.ts         # Database schema & types
└── package.json
```

## Key Features

### User Management
- User registration/login with username/password
- First registered user automatically becomes admin
- Company information and contact details
- Language preference (German/English)

### Booking Management
- Create/edit/delete ship docking reservations
- Detailed ship information (dimensions, passenger capacity)
- Pre-defined dock locations along the Danube
- Time-based editing restrictions (admin configurable)

### Admin Controls
- Configure editing time restrictions (cutoff times and blocked periods)
- Approve/reject booking change requests
- Manage all users and ships across the system
- Email notifications for change requests

### Change Request System
- When editing is restricted, users can submit change requests
- Visual comparison of old vs new values
- Admin approval workflow
- Email notifications to administrators

## API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user

### Bookings
- `GET /api/bookings` - Get user's bookings
- `POST /api/bookings` - Create booking
- `PUT /api/bookings/:id` - Update booking
- `DELETE /api/bookings/:id` - Delete booking
- `GET /api/bookings/:id/can-edit` - Check edit permissions

### Ships
- `GET /api/ships` - Get user's ships
- `POST /api/ships` - Create ship
- `PUT /api/ships/:id` - Update ship
- `DELETE /api/ships/:id` - Delete ship

### Docks
- `GET /api/docks` - Get all available docks

### Admin
- `GET /api/admin/settings` - Get admin settings
- `PUT /api/admin/settings` - Update admin settings
- `GET /api/admin/users` - Get all users
- `DELETE /api/admin/users/:id` - Delete user
- `GET /api/admin/ships` - Get all ships
- `GET /api/admin/change-requests` - Get change requests
- `PUT /api/admin/change-requests/:id` - Approve/reject change request

## Nginx Configuration (Optional)

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Docker Deployment (Alternative)

Create `Dockerfile`:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 5000

CMD ["npm", "start"]
```

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SESSION_SECRET=${SESSION_SECRET}
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=ship_docking
      - POSTGRES_USER=${PGUSER}
      - POSTGRES_PASSWORD=${PGPASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

## Default Data

### Pre-configured Docks (Austrian Danube)
1. Engelhartszell
2. Wesenufer
3. Schlögen
4. Obermühl
5. Untermühl
6. Aschach
7. Brandstatt
8. Ottensheim
9. Linz
10. Mauthausen
11. Enns
12. Grein
13. Sarmingstein

### First User Setup
- The first registered user automatically receives admin privileges
- Admin can then manage other users and configure system settings

## Troubleshooting

### Database Connection Issues
- Verify DATABASE_URL format and credentials
- Ensure PostgreSQL is running and accessible
- Check firewall settings for database port

### Email Not Working
- Verify Gmail credentials and app password
- Enable 2-factor authentication and generate app password
- Check Gmail security settings

### Session Issues
- Ensure SESSION_SECRET is set and secure
- Check that the session store is properly configured

### Build Issues
- Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`
- Verify Node.js version compatibility

## Security Considerations

1. **Session Secret**: Use a strong, random SESSION_SECRET
2. **Database**: Use strong database credentials
3. **HTTPS**: Enable HTTPS in production with proper SSL certificates
4. **Email**: Use Gmail app passwords, not main account password
5. **Environment Variables**: Never commit `.env` files to version control

## Monitoring & Logs

The application logs important events to the console. In production, consider:
- Setting up log rotation
- Using a logging service (e.g., Winston)
- Monitoring database performance
- Setting up health checks

## Backup Strategy

- Regular PostgreSQL database backups
- Environment configuration backup
- Application code versioning with Git

## Support

For issues during deployment:
1. Check application logs for error messages
2. Verify all environment variables are set correctly
3. Ensure database connectivity
4. Test email configuration if using notifications