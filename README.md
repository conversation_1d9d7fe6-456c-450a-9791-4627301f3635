# Ship Docking Management System (Schiffsanlege-Verwaltungssystem)

A comprehensive web application for managing ship docking reservations with administrative controls and multilingual support.

## Features

### User Management
- Secure registration and login system
- Company profiles with contact information  
- Language preference selection (German/English)
- First user automatically receives admin privileges

### Booking Management
- Create, edit, and delete docking reservations
- Detailed ship specifications (dimensions, capacity)
- Pre-configured Austrian Danube dock locations
- Time-based editing restrictions

### Administrative Controls
- Configure system-wide editing time restrictions
- Approve/reject booking change requests
- Manage all users and ships across the platform
- Email notifications for change requests

### Change Request Workflow
- Submit change requests during restricted periods
- Visual comparison of proposed changes
- Admin approval process with notes
- Automatic email notifications

## Tech Stack

- **Frontend**: React 18, TypeScript, Vite, TailwindCSS, shadcn/ui
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Passport.js with local strategy
- **Email**: Nodemailer with Gmail SMTP

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Gmail account (for email notifications)

### Development Setup

1. **Clone and install dependencies**
   ```bash
   npm install
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and email credentials
   ```

3. **Setup database**
   ```bash
   npm run db:push
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:5000`

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ship_docking
PGHOST=localhost
PGPORT=5432
PGDATABASE=ship_docking
PGUSER=your_user
PGPASSWORD=your_password

# Security
SESSION_SECRET=your_secure_session_secret

# Email (Optional)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_app_password
```

## Project Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Application pages
│   │   ├── hooks/         # Custom React hooks
│   │   ├── lib/           # Utility functions
│   │   └── contexts/      # React context providers
├── server/                # Express backend
│   ├── auth.ts           # Authentication middleware
│   ├── routes.ts         # API route handlers
│   ├── storage.ts        # Database operations
│   ├── email.ts          # Email notification service
│   └── index.ts          # Server entry point
├── shared/               # Shared TypeScript definitions
│   └── schema.ts         # Database schema and types
└── package.json
```

## Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run db:push` - Push database schema changes
- `npm run db:studio` - Open Drizzle Studio (database GUI)

## Default Dock Locations

The system comes pre-configured with 13 Austrian Danube River dock locations:

1. Engelhartszell
2. Wesenufer  
3. Schlögen
4. Obermühl
5. Untermühl
6. Aschach
7. Brandstatt
8. Ottensheim
9. Linz
10. Mauthausen
11. Enns
12. Grein
13. Sarmingstein

## User Roles

### Regular Users
- Manage personal ships and bookings
- Submit change requests during restricted periods
- View booking history and status

### Administrators
- Configure system-wide settings
- Manage all users and ships
- Approve/reject change requests
- Receive email notifications for requests

## Internationalization

The application supports German and English languages:
- Interface language selection during registration
- All user-facing text localized
- Date and time formatting per locale

## Production Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed production deployment instructions including:
- Environment configuration
- Database setup
- Docker deployment options
- Nginx configuration
- Security considerations

## Development Guidelines

### Database Changes
- Modify schema in `shared/schema.ts`
- Run `npm run db:push` to apply changes
- Use Drizzle Studio for database inspection

### Adding New Features
- Frontend components in `client/src/components/`
- API endpoints in `server/routes.ts`
- Database operations in `server/storage.ts`
- Shared types in `shared/schema.ts`

### Code Style
- TypeScript strict mode enabled
- ESLint and Prettier configured
- Component-based architecture
- RESTful API design

## Contributing

1. Follow existing code patterns and naming conventions
2. Add TypeScript types for all new data structures  
3. Include error handling for API endpoints
4. Test both German and English language interfaces
5. Verify admin and user permission flows

## License

This project is proprietary software for ship docking management operations.