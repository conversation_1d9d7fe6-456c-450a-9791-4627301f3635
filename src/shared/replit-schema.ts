import { pgTable, text, varchar, timestamp, jsonb, index, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table for Replit Auth
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table for Replit Auth
export const replitUsers = pgTable("replit_users", {
  id: varchar("id").primaryKey().notNull(), // Replit user ID (stable)
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  companyName: text("company_name"), // User-defined company name
  phoneNumber: text("phone_number"), // User-defined phone number
  language: text("language").default("de").notNull(), // de (Deutsch) or en (English)
  isAdmin: boolean("is_admin").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const upsertReplitUserSchema = createInsertSchema(replitUsers).omit({
  createdAt: true,
  updatedAt: true,
});

export type UpsertReplitUser = z.infer<typeof upsertReplitUserSchema>;
export type ReplitUser = typeof replitUsers.$inferSelect;