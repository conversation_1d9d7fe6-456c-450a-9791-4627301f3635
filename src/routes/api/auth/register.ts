import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { hashPassword, setSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/auth/register')({
  POST: async ({ request }) => {
    try {
      const userData = await request.json()

      if (!userData.username || !userData.password || !userData.email) {
        return json({ message: 'Benutzername, E-Mail und Passwort sind erforderlich' }, { status: 400 })
      }

      // Check if user already exists
      const existingUser = await storage.getUserByUsername(userData.username)
      if (existingUser) {
        return json({ message: 'Benutzername bereits vergeben' }, { status: 400 })
      }

      const existingEmail = await storage.getUserByEmail(userData.email)
      if (existingEmail) {
        return json({ message: 'E-Mail bereits vergeben' }, { status: 400 })
      }

      // Hash password and create user
      const hashedPassword = await hashPassword(userData.password)
      const user = await storage.createUser({
        ...userData,
        password: hashedPassword,
      })

      // Set session
      await setSession({
        userId: user.id,
        userEmail: user.email,
      })

      // Return user without password
      const { password: _, ...userWithoutPassword } = user
      return json(userWithoutPassword, { status: 201 })
    } catch (error) {
      console.error('Registration error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
