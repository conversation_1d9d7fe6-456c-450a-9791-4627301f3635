import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { clearSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/auth/logout')({
  POST: async () => {
    try {
      await clearSession()
      return json({ success: true })
    } catch (error) {
      console.error('Logout error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
