import { createAPIFileRoute } from '@tanstack/react-start/api'
import { json } from '@tanstack/react-start'
import { storage } from '../../../utils/storage'
import { getSession } from '../../../utils/session'

export const Route = createAPIFileRoute('/api/bookings/$id')({
  GET: async ({ params }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const bookingId = parseInt(params.id)
      if (isNaN(bookingId)) {
        return json({ message: 'Ungültige Buchungs-ID' }, { status: 400 })
      }

      const booking = await storage.getBooking(bookingId)
      if (!booking) {
        return json({ message: 'Buchung nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this booking
      if (booking.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      return json(booking)
    } catch (error) {
      console.error('Get booking error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  PUT: async ({ params, request }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const bookingId = parseInt(params.id)
      if (isNaN(bookingId)) {
        return json({ message: 'Ungültige Buchungs-ID' }, { status: 400 })
      }

      const booking = await storage.getBooking(bookingId)
      if (!booking) {
        return json({ message: 'Buchung nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this booking
      if (booking.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      const bookingData = await request.json()
      const updatedBooking = await storage.updateBooking(bookingId, bookingData)

      return json(updatedBooking)
    } catch (error) {
      console.error('Update booking error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },

  DELETE: async ({ params }) => {
    try {
      const session = await getSession()
      
      if (!session.userId) {
        return json({ message: 'Nicht authentifiziert' }, { status: 401 })
      }

      const bookingId = parseInt(params.id)
      if (isNaN(bookingId)) {
        return json({ message: 'Ungültige Buchungs-ID' }, { status: 400 })
      }

      const booking = await storage.getBooking(bookingId)
      if (!booking) {
        return json({ message: 'Buchung nicht gefunden' }, { status: 404 })
      }

      // Check if user owns this booking
      if (booking.userId !== session.userId) {
        return json({ message: 'Zugriff verweigert' }, { status: 403 })
      }

      const deleted = await storage.deleteBooking(bookingId)
      if (!deleted) {
        return json({ message: 'Fehler beim Löschen der Buchung' }, { status: 500 })
      }

      return json({ success: true })
    } catch (error) {
      console.error('Delete booking error:', error)
      return json({ message: 'Interner Serverfehler' }, { status: 500 })
    }
  },
})
