import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { useState } from 'react'
import { Ship as ShipIcon, Plus, Edit, Trash2, Users, Ruler } from 'lucide-react'

import { getSession } from '../utils/session'
import { storage } from '../utils/storage'
import type { Ship, User } from '../utils/storage'

// Server functions
const getUser = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    return null
  }
  
  const user = await storage.getUser(session.userId)
  if (!user) {
    return null
  }
  
  const { password: _, ...userWithoutPassword } = user
  return userWithoutPassword
})

const getShipsData = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    throw new Error('Not authenticated')
  }

  const ships = await storage.getShipsByUser(session.userId)
  return { ships }
})

export const Route = createFileRoute('/ships')({
  beforeLoad: async () => {
    const user = await getUser()
    if (!user) {
      throw redirect({ to: '/login' })
    }
  },
  loader: async () => {
    const [user, shipsData] = await Promise.all([
      getUser(),
      getShipsData(),
    ])
    return { user, ...shipsData }
  },
  component: ShipsPage,
})

function ShipsPage() {
  const { user, ships } = Route.useLoaderData()
  const [isShipFormOpen, setIsShipFormOpen] = useState(false)
  const [editingShip, setEditingShip] = useState<Ship | null>(null)

  const handleDeleteShip = async (shipId: number) => {
    if (confirm('Möchten Sie dieses Schiff wirklich löschen?')) {
      try {
        const response = await fetch(`/api/ships/${shipId}`, {
          method: 'DELETE',
        })
        
        if (response.ok) {
          // Refresh the page to show updated data
          window.location.reload()
        } else {
          alert('Fehler beim Löschen des Schiffs')
        }
      } catch (error) {
        alert('Fehler beim Löschen des Schiffs')
      }
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Meine Schiffe</h1>
                <p className="text-gray-500">Verwalten Sie Ihre Schiffsflotte</p>
              </div>
              <button
                onClick={() => setIsShipFormOpen(true)}
                className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
              >
                <Plus className="mr-2 h-4 w-4" /> Schiff hinzufügen
              </button>
            </div>

            {/* Ships Grid */}
            {ships.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {ships.map((ship: Ship) => (
                  <div key={ship.id} className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-6">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <ShipIcon className="h-8 w-8 text-indigo-600" />
                        </div>
                        <div className="ml-4 flex-1">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {ship.name}
                          </h3>
                          <p className="text-sm text-gray-500">{ship.type}</p>
                        </div>
                      </div>
                      
                      <div className="mt-4 space-y-2">
                        {ship.length && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Ruler className="h-4 w-4 mr-2" />
                            <span>{ship.length}m Länge</span>
                          </div>
                        )}
                        {ship.maxPassengers && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Users className="h-4 w-4 mr-2" />
                            <span>Max. {ship.maxPassengers} Passagiere</span>
                          </div>
                        )}
                      </div>

                      {ship.description && (
                        <div className="mt-4">
                          <p className="text-sm text-gray-600 line-clamp-3">
                            {ship.description}
                          </p>
                        </div>
                      )}

                      <div className="mt-6 flex justify-end space-x-3">
                        <button
                          onClick={() => setEditingShip(ship)}
                          className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Bearbeiten
                        </button>
                        <button
                          onClick={() => handleDeleteShip(ship.id)}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Löschen
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <ShipIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Keine Schiffe</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Fügen Sie Ihr erstes Schiff hinzu, um Buchungen erstellen zu können.
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => setIsShipFormOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Schiff hinzufügen
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
