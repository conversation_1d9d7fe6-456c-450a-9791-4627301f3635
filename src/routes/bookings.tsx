import { createFileRoute, redirect } from "@tanstack/react-router";
import { createServerFn } from "@tanstack/react-start";
import { useState } from "react";
import { Calendar, Ship as ShipIcon, Plus, Edit, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";

import { getSession } from "../utils/session";
import { storage } from "../utils/storage";
import type { BookingWithDetails, User } from "../utils/storage";
import AppLayout from "../components/app-layout";

// Server functions
const getUser = createServerFn({ method: "GET" }).handler(async () => {
  const session = await getSession();
  if (!session.userId) {
    return null;
  }

  const user = await storage.getUser(session.userId);
  if (!user) {
    return null;
  }

  const { password: _, ...userWithoutPassword } = user;
  return userWithoutPassword;
});

const getBookingsData = createServerFn({ method: "GET" }).handler(async () => {
  const session = await getSession();
  if (!session.userId) {
    throw new Error("Not authenticated");
  }

  const [bookings, ships, docks] = await Promise.all([
    storage.getBookingsByUser(session.userId),
    storage.getShipsByUser(session.userId),
    storage.getAllDocks(),
  ]);

  return { bookings, ships, docks };
});

export const Route = createFileRoute("/bookings")({
  beforeLoad: async () => {
    const user = await getUser();
    if (!user) {
      throw redirect({ to: "/login" });
    }
  },
  loader: async () => {
    const [user, bookingsData] = await Promise.all([
      getUser(),
      getBookingsData(),
    ]);
    return { user, ...bookingsData };
  },
  component: BookingsPage,
});

function BookingsPage() {
  const { user, bookings, ships, docks } = Route.useLoaderData();
  const [isBookingFormOpen, setIsBookingFormOpen] = useState(false);
  const [editingBooking, setEditingBooking] =
    useState<BookingWithDetails | null>(null);

  const handleDeleteBooking = async (bookingId: number) => {
    if (confirm("Möchten Sie diese Buchung wirklich löschen?")) {
      try {
        const response = await fetch(`/api/bookings/${bookingId}`, {
          method: "DELETE",
        });

        if (response.ok) {
          // Refresh the page to show updated data
          window.location.reload();
        } else {
          alert("Fehler beim Löschen der Buchung");
        }
      } catch (error) {
        alert("Fehler beim Löschen der Buchung");
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Bestätigt
          </span>
        );
      case "pending":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            Anfrage
          </span>
        );
      case "cancelled":
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Storniert
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <AppLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Buchungen</h1>
            <p className="text-gray-500">Verwalten Sie Ihre Hafenbuchungen</p>
          </div>
          <button
            type="button"
            onClick={() => setIsBookingFormOpen(true)}
            className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus className="mr-2 h-4 w-4" /> Neue Buchung
          </button>
        </div>

        {/* Bookings Table */}
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Alle Buchungen ({bookings.length})
            </h3>
          </div>

          {bookings.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Schiff
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Anlegestelle
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ankunft
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Abfahrt
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aktionen
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bookings.map((booking: BookingWithDetails) => (
                    <tr key={booking.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <ShipIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div className="text-sm font-medium text-gray-900">
                            {booking.ship.name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {booking.dock.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {booking.dock.location}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {format(new Date(booking.arrivalDate), "dd.MM.yyyy", {
                            locale: de,
                          })}
                        </div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(booking.arrivalDate), "HH:mm", {
                            locale: de,
                          })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {format(
                            new Date(booking.departureDate),
                            "dd.MM.yyyy",
                            { locale: de }
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(booking.departureDate), "HH:mm", {
                            locale: de,
                          })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(booking.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          type="button"
                          onClick={() => setEditingBooking(booking)}
                          className="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleDeleteBooking(booking.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                Keine Buchungen
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Erstellen Sie Ihre erste Buchung, um loszulegen.
              </p>
              <div className="mt-6">
                <button
                  type="button"
                  onClick={() => setIsBookingFormOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Neue Buchung
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
