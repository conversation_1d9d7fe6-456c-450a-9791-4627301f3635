import { createFileRoute, redirect } from '@tanstack/react-router'
import { createServerFn } from '@tanstack/react-start'
import { useState } from 'react'
import { User as UserIcon, Mail, Phone, Building, Calendar, Save } from 'lucide-react'
import { format } from 'date-fns'
import { de } from 'date-fns/locale'

import { getSession } from '../utils/session'
import { storage } from '../utils/storage'
import type { User } from '../utils/storage'

// Server functions
const getUser = createServerFn({ method: 'GET' }).handler(async () => {
  const session = await getSession()
  if (!session.userId) {
    return null
  }
  
  const user = await storage.getUser(session.userId)
  if (!user) {
    return null
  }
  
  const { password: _, ...userWithoutPassword } = user
  return userWithoutPassword
})

export const Route = createFileRoute('/profile')({
  beforeLoad: async () => {
    const user = await getUser()
    if (!user) {
      throw redirect({ to: '/login' })
    }
  },
  loader: async () => {
    const user = await getUser()
    return { user }
  },
  component: ProfilePage,
})

function ProfilePage() {
  const { user } = Route.useLoaderData()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    companyName: user?.companyName || '',
    phoneNumber: user?.phoneNumber || '',
    language: user?.language || 'de',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage('')

    try {
      const response = await fetch(`/api/users/${user?.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        setMessage('Profil erfolgreich aktualisiert')
        setIsEditing(false)
        // Refresh the page to show updated data
        setTimeout(() => window.location.reload(), 1000)
      } else {
        const data = await response.json()
        setMessage(data.message || 'Fehler beim Aktualisieren des Profils')
      }
    } catch (error) {
      setMessage('Fehler beim Aktualisieren des Profils')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      username: user?.username || '',
      email: user?.email || '',
      companyName: user?.companyName || '',
      phoneNumber: user?.phoneNumber || '',
      language: user?.language || 'de',
    })
    setIsEditing(false)
    setMessage('')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-3xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="space-y-6">
            {/* Header */}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Profil</h1>
              <p className="text-gray-500">Verwalten Sie Ihre Kontoinformationen</p>
            </div>

            {/* Profile Card */}
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <UserIcon className="h-10 w-10 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {user?.companyName}
                      </h3>
                      <p className="mt-1 max-w-2xl text-sm text-gray-500">
                        Mitglied seit {user?.createdAt ? format(new Date(user.createdAt), 'MMMM yyyy', { locale: de }) : ''}
                      </p>
                    </div>
                  </div>
                  {!isEditing && (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      Bearbeiten
                    </button>
                  )}
                </div>
              </div>

              {message && (
                <div className={`px-4 py-3 ${message.includes('erfolgreich') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                  {message}
                </div>
              )}

              <div className="border-t border-gray-200">
                {isEditing ? (
                  <form onSubmit={handleSubmit} className="px-4 py-5 sm:p-6 space-y-6">
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                          Benutzername
                        </label>
                        <input
                          type="text"
                          name="username"
                          id="username"
                          required
                          value={formData.username}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                          E-Mail-Adresse
                        </label>
                        <input
                          type="email"
                          name="email"
                          id="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                      </div>

                      <div className="sm:col-span-2">
                        <label htmlFor="companyName" className="block text-sm font-medium text-gray-700">
                          Firmenname
                        </label>
                        <input
                          type="text"
                          name="companyName"
                          id="companyName"
                          required
                          value={formData.companyName}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                      </div>

                      <div>
                        <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                          Telefonnummer
                        </label>
                        <input
                          type="tel"
                          name="phoneNumber"
                          id="phoneNumber"
                          value={formData.phoneNumber}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                      </div>

                      <div>
                        <label htmlFor="language" className="block text-sm font-medium text-gray-700">
                          Sprache
                        </label>
                        <select
                          name="language"
                          id="language"
                          value={formData.language}
                          onChange={handleInputChange}
                          className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        >
                          <option value="de">Deutsch</option>
                          <option value="en">English</option>
                        </select>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Abbrechen
                      </button>
                      <button
                        type="submit"
                        disabled={isLoading}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50"
                      >
                        <Save className="mr-2 h-4 w-4" />
                        {isLoading ? 'Speichern...' : 'Speichern'}
                      </button>
                    </div>
                  </form>
                ) : (
                  <dl className="px-4 py-5 sm:p-6">
                    <div className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                      <div>
                        <dt className="text-sm font-medium text-gray-500 flex items-center">
                          <UserIcon className="h-4 w-4 mr-2" />
                          Benutzername
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900">{user?.username}</dd>
                      </div>

                      <div>
                        <dt className="text-sm font-medium text-gray-500 flex items-center">
                          <Mail className="h-4 w-4 mr-2" />
                          E-Mail-Adresse
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900">{user?.email}</dd>
                      </div>

                      <div className="sm:col-span-2">
                        <dt className="text-sm font-medium text-gray-500 flex items-center">
                          <Building className="h-4 w-4 mr-2" />
                          Firmenname
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900">{user?.companyName}</dd>
                      </div>

                      <div>
                        <dt className="text-sm font-medium text-gray-500 flex items-center">
                          <Phone className="h-4 w-4 mr-2" />
                          Telefonnummer
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900">{user?.phoneNumber || 'Nicht angegeben'}</dd>
                      </div>

                      <div>
                        <dt className="text-sm font-medium text-gray-500 flex items-center">
                          <Calendar className="h-4 w-4 mr-2" />
                          Mitglied seit
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900">
                          {user?.createdAt ? format(new Date(user.createdAt), 'dd. MMMM yyyy', { locale: de }) : 'Unbekannt'}
                        </dd>
                      </div>
                    </div>
                  </dl>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
