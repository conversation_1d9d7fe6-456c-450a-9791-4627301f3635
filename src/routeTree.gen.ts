/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as ShipsImport } from './routes/ships'
import { Route as ProfileImport } from './routes/profile'
import { Route as LoginImport } from './routes/login'
import { Route as BookingsImport } from './routes/bookings'
import { Route as AdminImport } from './routes/admin'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const ShipsRoute = ShipsImport.update({
  id: '/ships',
  path: '/ships',
  getParentRoute: () => rootRoute,
} as any)

const ProfileRoute = ProfileImport.update({
  id: '/profile',
  path: '/profile',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const BookingsRoute = BookingsImport.update({
  id: '/bookings',
  path: '/bookings',
  getParentRoute: () => rootRoute,
} as any)

const AdminRoute = AdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminImport
      parentRoute: typeof rootRoute
    }
    '/bookings': {
      id: '/bookings'
      path: '/bookings'
      fullPath: '/bookings'
      preLoaderRoute: typeof BookingsImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/profile': {
      id: '/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof ProfileImport
      parentRoute: typeof rootRoute
    }
    '/ships': {
      id: '/ships'
      path: '/ships'
      fullPath: '/ships'
      preLoaderRoute: typeof ShipsImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/bookings': typeof BookingsRoute
  '/login': typeof LoginRoute
  '/profile': typeof ProfileRoute
  '/ships': typeof ShipsRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/bookings': typeof BookingsRoute
  '/login': typeof LoginRoute
  '/profile': typeof ProfileRoute
  '/ships': typeof ShipsRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/bookings': typeof BookingsRoute
  '/login': typeof LoginRoute
  '/profile': typeof ProfileRoute
  '/ships': typeof ShipsRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/admin' | '/bookings' | '/login' | '/profile' | '/ships'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/admin' | '/bookings' | '/login' | '/profile' | '/ships'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/bookings'
    | '/login'
    | '/profile'
    | '/ships'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRoute
  BookingsRoute: typeof BookingsRoute
  LoginRoute: typeof LoginRoute
  ProfileRoute: typeof ProfileRoute
  ShipsRoute: typeof ShipsRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRoute,
  BookingsRoute: BookingsRoute,
  LoginRoute: LoginRoute,
  ProfileRoute: ProfileRoute,
  ShipsRoute: ShipsRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/admin",
        "/bookings",
        "/login",
        "/profile",
        "/ships"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/admin": {
      "filePath": "admin.tsx"
    },
    "/bookings": {
      "filePath": "bookings.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/profile": {
      "filePath": "profile.tsx"
    },
    "/ships": {
      "filePath": "ships.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
