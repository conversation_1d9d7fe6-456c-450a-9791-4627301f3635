import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Edit, Trash2, Ship, MapPin, Calendar, Clock, FileText } from "lucide-react";

import { BookingWithDetails } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { formatDate, formatTime } from "@/lib/date-utils";
import { useToast } from "@/hooks/use-toast";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import BookingForm from "./booking-form";
import ConfirmDialog from "./confirm-dialog";

interface BookingCardProps {
  booking: BookingWithDetails;
  onDelete?: () => void;
}

export default function BookingCard({ booking, onDelete }: BookingCardProps) {
  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Delete booking mutation
  const deleteBookingMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/bookings/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      toast({
        title: "Buchung gelöscht",
        description: "Die Buchung wurde erfolgreich gelöscht.",
      });
      if (onDelete) onDelete();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: `Fehler beim Löschen der Buchung: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Format arrival and departure dates
  const arrivalDate = new Date(booking.arrivalDate);
  const departureDate = new Date(booking.departureDate);

  // Get status badge color
  const getStatusBadge = () => {
    switch (booking.status) {
      case "confirmed":
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Bestätigt</Badge>;
      case "pending":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Anfrage</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Storniert</Badge>;
      default:
        return <Badge variant="outline">{booking.status}</Badge>;
    }
  };

  return (
    <Card className="h-full flex flex-col shadow-sm hover:shadow transition-shadow duration-200">
      <CardHeader className="pb-2 flex flex-row justify-between items-start bg-primary/5 border-b">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Ship className="h-4 w-4 text-primary" />
            <h3 className="font-semibold text-lg">{booking.ship.name}</h3>
          </div>
          <div className="flex flex-wrap items-center gap-2 text-sm text-gray-500">
            <MapPin className="h-3.5 w-3.5" />
            <span>{booking.dock.name}</span>
            <div className="ml-auto">{getStatusBadge()}</div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="py-4 flex-grow">
        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <Calendar className="h-4 w-4 text-gray-400 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Ankunft</p>
              <div className="flex items-center space-x-2">
                <p className="text-sm">{formatDate(arrivalDate)}</p>
                <span className="text-gray-400">|</span>
                <div className="flex items-center">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  <p className="text-sm">{formatTime(arrivalDate)}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Calendar className="h-4 w-4 text-gray-400 mt-0.5" />
            <div className="space-y-1">
              <p className="text-sm font-medium">Abfahrt</p>
              <div className="flex items-center space-x-2">
                <p className="text-sm">{formatDate(departureDate)}</p>
                <span className="text-gray-400">|</span>
                <div className="flex items-center">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  <p className="text-sm">{formatTime(departureDate)}</p>
                </div>
              </div>
            </div>
          </div>

          {booking.row && (
            <div className="flex items-start space-x-3">
              <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Reihe</p>
                <p className="text-sm">{booking.row}</p>
              </div>
            </div>
          )}

          {booking.notes && (
            <div className="flex items-start space-x-3">
              <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium">Anmerkungen</p>
                <p className="text-sm">{booking.notes}</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-end gap-2">
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <Edit className="h-3.5 w-3.5 mr-1" /> Bearbeiten
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <BookingForm bookingId={booking.id} />
          </DialogContent>
        </Dialog>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 text-destructive border-destructive hover:bg-destructive/10"
          onClick={() => setIsDeleteDialogOpen(true)}
        >
          <Trash2 className="h-3.5 w-3.5 mr-1" /> Löschen
        </Button>
      </CardFooter>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Buchung löschen"
        description="Sind Sie sicher, dass Sie diese Buchung löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden."
        onConfirm={() => deleteBookingMutation.mutate(booking.id)}
      />
    </Card>
  );
}
