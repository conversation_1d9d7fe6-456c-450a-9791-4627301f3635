import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { CalendarIcon, Clock } from "lucide-react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { BookingWithDetails, Ship, Dock } from "@shared/schema";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import { formatDate, formatTime, dateTimeToISOString, isoToDateString, isoToTimeString } from "@/lib/date-utils";

const editRequestSchema = z.object({
  shipId: z.number().min(1, "Bitte wählen Sie ein Schiff"),
  dockId: z.number().min(1, "Bitte wählen Sie eine Lände"),
  arrivalDate: z.string().min(1, "Ankunftsdatum ist erforderlich"),
  arrivalTime: z.string().min(1, "Ankunftszeit ist erforderlich"),
  departureDate: z.string().min(1, "Abfahrtsdatum ist erforderlich"),
  departureTime: z.string().min(1, "Abfahrtszeit ist erforderlich"),
  row: z.string().optional(),
  notes: z.string().optional(),
});

type EditRequestFormValues = z.infer<typeof editRequestSchema>;

interface BookingEditRequestFormProps {
  booking: BookingWithDetails;
  ships: Ship[];
  docks: Dock[];
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function BookingEditRequestForm({ 
  booking, 
  ships, 
  docks, 
  onSuccess, 
  onCancel 
}: BookingEditRequestFormProps) {
  const { toast } = useToast();
  const [arrivalCalendarOpen, setArrivalCalendarOpen] = useState(false);
  const [departureCalendarOpen, setDepartureCalendarOpen] = useState(false);

  const form = useForm<EditRequestFormValues>({
    resolver: zodResolver(editRequestSchema),
    defaultValues: {
      shipId: booking.shipId,
      dockId: booking.dockId,
      arrivalDate: isoToDateString(booking.arrivalDate),
      arrivalTime: isoToTimeString(booking.arrivalDate),
      departureDate: isoToDateString(booking.departureDate),
      departureTime: isoToTimeString(booking.departureDate),
      row: booking.row || "",
      notes: booking.notes || "",
    },
  });

  const createRequestMutation = useMutation({
    mutationFn: async (requestedChanges: any) => {
      const res = await apiRequest("POST", "/api/booking-edit-requests", {
        bookingId: booking.id,
        requestedChanges,
      });
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/bookings"] });
      toast({
        title: "Bearbeitungsanfrage gesendet",
        description: "Ihre Anfrage wurde an die Admins weitergeleitet.",
      });
      onSuccess?.();
    },
    onError: (error: Error) => {
      toast({
        title: "Fehler",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  function onSubmit(values: EditRequestFormValues) {
    const requestedChanges = {
      shipId: values.shipId,
      dockId: values.dockId,
      arrivalDate: dateTimeToISOString(values.arrivalDate, values.arrivalTime),
      departureDate: dateTimeToISOString(values.departureDate, values.departureTime),
      row: values.row || null,
      notes: values.notes || null,
    };

    createRequestMutation.mutate(requestedChanges);
  }

  const selectedArrivalDate = form.watch("arrivalDate");
  const selectedDepartureDate = form.watch("departureDate");

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Bearbeitungsanfrage für Buchung #{booking.id}
        </CardTitle>
        <p className="text-sm text-gray-600">
          Da die Bearbeitungszeit abgelaufen ist, müssen Änderungen von einem Admin genehmigt werden.
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="shipId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Schiff</FormLabel>
                    <Select 
                      value={field.value?.toString()} 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Schiff auswählen" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ships.map((ship) => (
                          <SelectItem key={ship.id} value={ship.id.toString()}>
                            {ship.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dockId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ort</FormLabel>
                    <Select 
                      value={field.value?.toString()} 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Ort auswählen" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {docks.map((dock) => (
                          <SelectItem key={dock.id} value={dock.id.toString()}>
                            {dock.name} - {dock.location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="arrivalDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Ankunftsdatum</FormLabel>
                      <Popover open={arrivalCalendarOpen} onOpenChange={setArrivalCalendarOpen}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                formatDate(new Date(field.value.split('.').reverse().join('-')))
                              ) : (
                                <span>Datum auswählen</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value.split('.').reverse().join('-')) : undefined}
                            onSelect={(date) => {
                              if (date) {
                                field.onChange(formatDate(date));
                                setArrivalCalendarOpen(false);
                              }
                            }}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="arrivalTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ankunftszeit</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="departureDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Abfahrtsdatum</FormLabel>
                      <Popover open={departureCalendarOpen} onOpenChange={setDepartureCalendarOpen}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                formatDate(new Date(field.value.split('.').reverse().join('-')))
                              ) : (
                                <span>Datum auswählen</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value ? new Date(field.value.split('.').reverse().join('-')) : undefined}
                            onSelect={(date) => {
                              if (date) {
                                field.onChange(formatDate(date));
                                setDepartureCalendarOpen(false);
                              }
                            }}
                            disabled={(date) => {
                              const arrivalDateObj = selectedArrivalDate ? 
                                new Date(selectedArrivalDate.split('.').reverse().join('-')) : 
                                new Date();
                              return date < arrivalDateObj;
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="departureTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Abfahrtszeit</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="row"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reihe (optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="z.B. A, B, C..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bemerkungen (optional)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Zusätzliche Informationen zur Buchung..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-4 justify-end">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                disabled={createRequestMutation.isPending}
              >
                Abbrechen
              </Button>
              <Button 
                type="submit"
                disabled={createRequestMutation.isPending}
              >
                {createRequestMutation.isPending ? "Wird gesendet..." : "Anfrage senden"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}