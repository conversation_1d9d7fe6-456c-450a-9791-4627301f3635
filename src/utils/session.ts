import { createServerFn } from '@tanstack/react-start'
import { getCookie, setCookie, deleteCookie } from 'vinxi/http'
import { scrypt, randomBytes, timingSafeEqual } from 'crypto'
import { promisify } from 'util'

const scryptAsync = promisify(scrypt)

export interface SessionData {
  userId?: number
  userEmail?: string
}

// Session management functions
export async function getSession(): Promise<SessionData> {
  const sessionCookie = getCookie('session')
  if (!sessionCookie) {
    return {}
  }

  try {
    // In a real app, you'd decrypt/verify the session cookie
    // For now, we'll use a simple JSON parse (not secure for production)
    return JSON.parse(sessionCookie)
  } catch {
    return {}
  }
}

export async function setSession(data: SessionData) {
  // In a real app, you'd encrypt/sign the session data
  // For now, we'll use a simple JSON stringify (not secure for production)
  setCookie('session', JSON.stringify(data), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60, // 7 days
  })
}

export async function clearSession() {
  deleteCookie('session')
}

// Password hashing functions
export async function hashPassword(password: string): Promise<string> {
  const salt = randomBytes(16).toString('hex')
  const buf = (await scryptAsync(password, salt, 64)) as Buffer
  return `${buf.toString('hex')}.${salt}`
}

export async function comparePasswords(supplied: string, stored: string): Promise<boolean> {
  const [hashed, salt] = stored.split('.')
  const hashedBuf = Buffer.from(hashed, 'hex')
  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer
  return timingSafeEqual(hashedBuf, suppliedBuf)
}

// Server function to get current user session
export const useAppSession = createServerFn({ method: 'GET' }).handler(async () => {
  return {
    data: await getSession(),
  }
})
