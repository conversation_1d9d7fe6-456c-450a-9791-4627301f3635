// Re-export the storage functionality from the server
// This will be used by server functions in TanStack Start

import { storage } from '../../server/storage'
import type { IStorage } from '../../server/storage'

export { storage }
export type { IStorage }

// Re-export types from shared schema
export type {
  User,
  Ship,
  Dock,
  Booking,
  BookingWithDetails,
  AdminSettings,
  BookingEditRequest,
  BookingEditRequestWithDetails,
  InsertUser,
  InsertShip,
  InsertDock,
  InsertBooking,
  InsertAdminSettings,
  InsertBookingEditRequest,
} from '../../shared/schema'
